import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';

/// خدمة تصدير التقارير المحسنة
class EnhancedReportExportService {
  /// تصدير تقرير شامل بصيغة PDF
  static Future<String?> exportComprehensiveReportPDF({
    required BuildContext context,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<CashboxModel> cashboxes,
    required List<ClientAccountModel> accounts,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      debugPrint('🔄 بدء تصدير التقرير الشامل بصيغة PDF...');

      // السماح للمستخدم باختيار مجلد الحفظ
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();

      if (selectedDirectory == null) {
        debugPrint('❌ تم إلغاء اختيار المجلد');
        return null;
      }

      // إنشاء مستند PDF
      final pdf = pw.Document();

      // إضافة الصفحة الأولى
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (context) => [
            _buildPDFHeader(),
            _buildPDFOverview(
                clients, farms, irrigations, payments, cashboxes, accounts),
            _buildPDFClientsSection(clients),
            _buildPDFFarmsSection(farms),
            _buildPDFIrrigationsSection(irrigations),
            _buildPDFPaymentsSection(payments),
            _buildPDFCashboxesSection(cashboxes),
            _buildPDFAccountsSection(accounts),
            _buildPDFFooter(startDate, endDate),
          ],
        ),
      );

      // حفظ الملف
      final timestamp =
          DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final fileName = 'comprehensive_report_$timestamp.pdf';
      final filePath = '$selectedDirectory/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      if (context.mounted) {
        _showExportSuccessDialog(context, filePath, 'PDF');
      }

      debugPrint('✅ تم تصدير التقرير الشامل بصيغة PDF: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ خطأ في تصدير التقرير بصيغة PDF: $e');
      if (context.mounted) {
        _showExportErrorDialog(context, 'خطأ في تصدير التقرير بصيغة PDF: $e');
      }
      return null;
    }
  }

  /// تصدير تقرير بصيغة Excel
  static Future<String?> exportReportExcel({
    required BuildContext context,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<CashboxModel> cashboxes,
    required List<ClientAccountModel> accounts,
    String reportType = 'comprehensive',
  }) async {
    try {
      debugPrint('🔄 بدء تصدير التقرير بصيغة Excel...');

      // السماح للمستخدم باختيار مجلد الحفظ
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();

      if (selectedDirectory == null) {
        debugPrint('❌ تم إلغاء اختيار المجلد');
        return null;
      }

      // إنشاء ملف Excel
      final excel = Excel.createExcel();

      // إضافة ورقة العملاء
      _addClientsSheet(excel, clients);

      // إضافة ورقة المزارع
      _addFarmsSheet(excel, farms);

      // إضافة ورقة التسقيات
      _addIrrigationsSheet(excel, irrigations);

      // إضافة ورقة المدفوعات
      _addPaymentsSheet(excel, payments);

      // إضافة ورقة الصناديق
      _addCashboxesSheet(excel, cashboxes);

      // إضافة ورقة الحسابات
      _addAccountsSheet(excel, accounts);

      // إضافة ورقة الإحصائيات
      _addStatisticsSheet(
          excel, clients, farms, irrigations, payments, cashboxes, accounts);

      // حفظ الملف
      final timestamp =
          DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final fileName = '${reportType}_report_$timestamp.xlsx';
      final filePath = '$selectedDirectory/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      if (context.mounted) {
        _showExportSuccessDialog(context, filePath, 'Excel');
      }

      debugPrint('✅ تم تصدير التقرير بصيغة Excel: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ خطأ في تصدير التقرير بصيغة Excel: $e');
      if (context.mounted) {
        _showExportErrorDialog(context, 'خطأ في تصدير التقرير بصيغة Excel: $e');
      }
      return null;
    }
  }

  /// تصدير تقرير بصيغة CSV
  static Future<String?> exportReportCSV({
    required BuildContext context,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<CashboxModel> cashboxes,
    required List<ClientAccountModel> accounts,
    String reportType = 'comprehensive',
  }) async {
    try {
      debugPrint('🔄 بدء تصدير التقرير بصيغة CSV...');

      // السماح للمستخدم باختيار مجلد الحفظ
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();

      if (selectedDirectory == null) {
        debugPrint('❌ تم إلغاء اختيار المجلد');
        return null;
      }

      // إنشاء بيانات CSV
      final csvData = <List<dynamic>>[];

      // إضافة رؤوس الأعمدة
      csvData.add([
        'نوع البيانات',
        'المعرف',
        'الاسم',
        'التفاصيل',
        'التاريخ',
        'القيمة',
        'الحالة',
      ]);

      // إضافة بيانات العملاء
      for (final client in clients) {
        csvData.add([
          'عميل',
          client.id,
          client.name,
          '${client.phone} - ${client.address}',
          client.createdAt?.toIso8601String() ?? '',
          '',
          client.status,
        ]);
      }

      // إضافة بيانات المزارع
      for (final farm in farms) {
        csvData.add([
          'مزرعة',
          farm.id,
          farm.name,
          '${farm.location} - ${farm.size} دونم',
          farm.createdAt?.toIso8601String() ?? '',
          farm.size.toString(),
          farm.status,
        ]);
      }

      // إضافة بيانات التسقيات
      for (final irrigation in irrigations) {
        csvData.add([
          'تسقية',
          irrigation.id,
          irrigation.farmName,
          '${irrigation.duration} ساعة - ${irrigation.dieselConsumption} لتر',
          irrigation.date?.toIso8601String() ?? '',
          irrigation.cost.toString(),
          irrigation.status,
        ]);
      }

      // إضافة بيانات المدفوعات
      for (final payment in payments) {
        csvData.add([
          'دفعة',
          payment.id,
          payment.clientName,
          payment.description,
          payment.date?.toIso8601String() ?? '',
          payment.amount.toString(),
          payment.status,
        ]);
      }

      // تحويل البيانات إلى CSV
      final csvString = const CsvToListConverter().convert(csvData);

      // حفظ الملف
      final timestamp =
          DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final fileName = '${reportType}_report_$timestamp.csv';
      final filePath = '$selectedDirectory/$fileName';

      final file = File(filePath);
      await file.writeAsString(csvString);

      if (context.mounted) {
        _showExportSuccessDialog(context, filePath, 'CSV');
      }

      debugPrint('✅ تم تصدير التقرير بصيغة CSV: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ خطأ في تصدير التقرير بصيغة CSV: $e');
      if (context.mounted) {
        _showExportErrorDialog(context, 'خطأ في تصدير التقرير بصيغة CSV: $e');
      }
      return null;
    }
  }

  /// تصدير تقرير بصيغة JSON
  static Future<String?> exportReportJSON({
    required BuildContext context,
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<CashboxModel> cashboxes,
    required List<ClientAccountModel> accounts,
    String reportType = 'comprehensive',
  }) async {
    try {
      debugPrint('🔄 بدء تصدير التقرير بصيغة JSON...');

      // السماح للمستخدم باختيار مجلد الحفظ
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();

      if (selectedDirectory == null) {
        debugPrint('❌ تم إلغاء اختيار المجلد');
        return null;
      }

      // إنشاء بيانات JSON
      final reportData = {
        'report_info': {
          'type': reportType,
          'generated_at': DateTime.now().toIso8601String(),
          'total_clients': clients.length,
          'total_farms': farms.length,
          'total_irrigations': irrigations.length,
          'total_payments': payments.length,
          'total_cashboxes': cashboxes.length,
          'total_accounts': accounts.length,
        },
        'clients': clients.map((client) => client.toJson()).toList(),
        'farms': farms.map((farm) => farm.toJson()).toList(),
        'irrigations':
            irrigations.map((irrigation) => irrigation.toJson()).toList(),
        'payments': payments.map((payment) => payment.toJson()).toList(),
        'cashboxes': cashboxes.map((cashbox) => cashbox.toJson()).toList(),
        'accounts': accounts.map((account) => account.toJson()).toList(),
      };

      // تحويل البيانات إلى JSON
      final jsonString = const JsonEncoder.withIndent('  ').convert(reportData);

      // حفظ الملف
      final timestamp =
          DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final fileName = '${reportType}_report_$timestamp.json';
      final filePath = '$selectedDirectory/$fileName';

      final file = File(filePath);
      await file.writeAsString(jsonString, encoding: utf8);

      if (context.mounted) {
        _showExportSuccessDialog(context, filePath, 'JSON');
      }

      debugPrint('✅ تم تصدير التقرير بصيغة JSON: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('❌ خطأ في تصدير التقرير بصيغة JSON: $e');
      if (context.mounted) {
        _showExportErrorDialog(context, 'خطأ في تصدير التقرير بصيغة JSON: $e');
      }
      return null;
    }
  }

  // دوال مساعدة لإنشاء PDF

  static pw.Widget _buildPDFHeader() {
    return pw.Header(
      level: 0,
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'تقرير إدارة التسقيات الشامل',
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.Text(
            DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()),
            style: pw.TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFOverview(
    List<ClientModel> clients,
    List<FarmModel> farms,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    List<CashboxModel> cashboxes,
    List<ClientAccountModel> accounts,
  ) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'نظرة عامة',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Table(
            border: pw.TableBorder.all(),
            children: [
              pw.TableRow(
                children: [
                  pw.Text('العملاء'),
                  pw.Text('المزارع'),
                  pw.Text('التسقيات'),
                  pw.Text('المدفوعات'),
                ],
              ),
              pw.TableRow(
                children: [
                  pw.Text(clients.length.toString()),
                  pw.Text(farms.length.toString()),
                  pw.Text(irrigations.length.toString()),
                  pw.Text(payments.length.toString()),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFClientsSection(List<ClientModel> clients) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'العملاء',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          ...clients.map((client) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 5),
                child: pw.Text('${client.name} - ${client.phone}'),
              )),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFFarmsSection(List<FarmModel> farms) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'المزارع',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          ...farms.map((farm) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 5),
                child: pw.Text('${farm.name} - ${farm.location}'),
              )),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFIrrigationsSection(
      List<IrrigationModel> irrigations) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'التسقيات',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          ...irrigations.map((irrigation) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 5),
                child: pw.Text('${irrigation.farmName} - ${irrigation.date}'),
              )),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFPaymentsSection(List<PaymentModel> payments) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'المدفوعات',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          ...payments.map((payment) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 5),
                child: pw.Text('${payment.clientName} - ${payment.amount}'),
              )),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFCashboxesSection(List<CashboxModel> cashboxes) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الصناديق',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          ...cashboxes.map((cashbox) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 5),
                child: pw.Text('${cashbox.name} - ${cashbox.balance}'),
              )),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFAccountsSection(List<ClientAccountModel> accounts) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الحسابات',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          ...accounts.map((account) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 5),
                child: pw.Text('${account.clientName} - ${account.balance}'),
              )),
        ],
      ),
    );
  }

  static pw.Widget _buildPDFFooter(DateTime? startDate, DateTime? endDate) {
    return pw.Footer(
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'تم إنشاؤه بواسطة تطبيق إدارة التسقيات',
            style: pw.TextStyle(fontSize: 10),
          ),
          if (startDate != null && endDate != null)
            pw.Text(
              'الفترة: ${DateFormat('yyyy-MM-dd').format(startDate)} إلى ${DateFormat('yyyy-MM-dd').format(endDate)}',
              style: pw.TextStyle(fontSize: 10),
            ),
        ],
      ),
    );
  }

  // دوال مساعدة لإنشاء Excel

  static void _addClientsSheet(Excel excel, List<ClientModel> clients) {
    final sheet = excel['العملاء'];

    // إضافة رؤوس الأعمدة
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'المعرف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value =
        'الاسم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value =
        'الهاتف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value =
        'العنوان';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value =
        'الحالة';

    // إضافة البيانات
    for (int i = 0; i < clients.length; i++) {
      final client = clients[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = client.id;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = client.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = client.phone;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = client.address;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = client.status;
    }
  }

  static void _addFarmsSheet(Excel excel, List<FarmModel> farms) {
    final sheet = excel['المزارع'];

    // إضافة رؤوس الأعمدة
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'المعرف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value =
        'الاسم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value =
        'الموقع';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value =
        'المساحة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value =
        'الحالة';

    // إضافة البيانات
    for (int i = 0; i < farms.length; i++) {
      final farm = farms[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = farm.id;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = farm.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = farm.location;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = farm.size;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = farm.status;
    }
  }

  static void _addIrrigationsSheet(
      Excel excel, List<IrrigationModel> irrigations) {
    final sheet = excel['التسقيات'];

    // إضافة رؤوس الأعمدة
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'المعرف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value =
        'اسم المزرعة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value =
        'التاريخ';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value =
        'المدة';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value =
        'استهلاك الديزل';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0)).value =
        'التكلفة';

    // إضافة البيانات
    for (int i = 0; i < irrigations.length; i++) {
      final irrigation = irrigations[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = irrigation.id;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = irrigation.farmName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = irrigation.date?.toIso8601String();
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = irrigation.duration;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = irrigation.dieselConsumption;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = irrigation.cost;
    }
  }

  static void _addPaymentsSheet(Excel excel, List<PaymentModel> payments) {
    final sheet = excel['المدفوعات'];

    // إضافة رؤوس الأعمدة
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'المعرف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value =
        'اسم العميل';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value =
        'المبلغ';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value =
        'الوصف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 0)).value =
        'التاريخ';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: 0)).value =
        'الحالة';

    // إضافة البيانات
    for (int i = 0; i < payments.length; i++) {
      final payment = payments[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = payment.id;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = payment.clientName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = payment.amount;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = payment.description;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = payment.date?.toIso8601String();
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = payment.status;
    }
  }

  static void _addCashboxesSheet(Excel excel, List<CashboxModel> cashboxes) {
    final sheet = excel['الصناديق'];

    // إضافة رؤوس الأعمدة
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'المعرف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value =
        'الاسم';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value =
        'الرصيد';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value =
        'الوصف';

    // إضافة البيانات
    for (int i = 0; i < cashboxes.length; i++) {
      final cashbox = cashboxes[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = cashbox.id;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = cashbox.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = cashbox.balance;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = cashbox.description;
    }
  }

  static void _addAccountsSheet(
      Excel excel, List<ClientAccountModel> accounts) {
    final sheet = excel['الحسابات'];

    // إضافة رؤوس الأعمدة
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'المعرف';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value =
        'اسم العميل';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value =
        'الرصيد';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 0)).value =
        'آخر تحديث';

    // إضافة البيانات
    for (int i = 0; i < accounts.length; i++) {
      final account = accounts[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = account.id;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = account.clientName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = account.balance;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = account.updatedAt?.toIso8601String();
    }
  }

  static void _addStatisticsSheet(
    Excel excel,
    List<ClientModel> clients,
    List<FarmModel> farms,
    List<IrrigationModel> irrigations,
    List<PaymentModel> payments,
    List<CashboxModel> cashboxes,
    List<ClientAccountModel> accounts,
  ) {
    final sheet = excel['الإحصائيات'];

    // إضافة رؤوس الأعمدة
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'النوع';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value =
        'العدد';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 0)).value =
        'القيمة الإجمالية';

    // إضافة البيانات
    final data = [
      ['العملاء', clients.length, ''],
      [
        'المزارع',
        farms.length,
        farms.fold(0.0, (sum, farm) => sum + farm.size).toString()
      ],
      [
        'التسقيات',
        irrigations.length,
        irrigations
            .fold(0.0, (sum, irrigation) => sum + irrigation.cost)
            .toString()
      ],
      [
        'المدفوعات',
        payments.length,
        payments.fold(0.0, (sum, payment) => sum + payment.amount).toString()
      ],
      [
        'الصناديق',
        cashboxes.length,
        cashboxes.fold(0.0, (sum, cashbox) => sum + cashbox.balance).toString()
      ],
      [
        'الحسابات',
        accounts.length,
        accounts.fold(0.0, (sum, account) => sum + account.balance).toString()
      ],
    ];

    for (int i = 0; i < data.length; i++) {
      final row = i + 1;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = data[i][0];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = data[i][1];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = data[i][2];
    }
  }

  // دوال مساعدة لعرض الحوارات

  static void _showExportSuccessDialog(
      BuildContext context, String filePath, String format) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تم التصدير بنجاح'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم تصدير التقرير بصيغة $format بنجاح'),
            const SizedBox(height: 8),
            Text(
              'المسار: $filePath',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  static void _showExportErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ في التصدير'),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}

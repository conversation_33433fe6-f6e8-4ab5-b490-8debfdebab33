import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:contacts_service/contacts_service.dart'; // معطلة مؤقتاً لحل مشاكل البناء
// import 'package:permission_handler/permission_handler.dart'; // معطلة مؤقتاً
import 'package:untitled/data/models/client_model.dart';
// تم إزالة imports غير مستخدمة مؤقتاً
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/presentation/pages/client/contacts_picker_page.dart';
import 'package:untitled/core/services/client_notification_service.dart';

/// صفحة إضافة عميل جديد مع نظام الحسابات المتكامل
class AddClientPageNew extends StatefulWidget {
  final ClientModel? client; // للتعديل

  const AddClientPageNew({
    super.key,
    this.client,
  });

  @override
  State<AddClientPageNew> createState() => _AddClientPageNewState();
}

class _AddClientPageNewState extends State<AddClientPageNew> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();
  final _farmNameController = TextEditingController();
  final _farmNotesController = TextEditingController();

  bool _isEditing = false;
  final bool _isLoadingContacts = false;
  int? _lastCreatedClientId;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.client != null;

    if (_isEditing) {
      _nameController.text = widget.client!.name;
      _phoneController.text = widget.client!.phone ?? '';
      _notesController.text = widget.client!.notes ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    _farmNameController.dispose();
    _farmNotesController.dispose();
    super.dispose();
  }

  /// حفظ العميل والمزرعة والحساب
  Future<void> _saveClient() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من إدخال اسم المزرعة للعملاء الجدد (إجباري)
    if (!_isEditing && _farmNameController.text.trim().isEmpty) {
      _showMessage('يرجى إدخال اسم المزرعة');
      return;
    }

    final now = DateTime.now();

    try {
      if (_isEditing) {
        // تعديل العميل الموجود
        final updatedClient = widget.client!.copyWith(
          name: _nameController.text.trim(),
          phone: _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
          updatedAt: now,
        );

        context.read<ClientBloc>().add(UpdateClient(updatedClient));

        // إرسال إشعار تحديث العميل
        try {
          await ClientNotificationService.notifyClientUpdated(updatedClient);
        } catch (e) {
          debugPrint('❌ خطأ في إرسال إشعار تحديث العميل: $e');
        }
      } else {
        // إضافة عميل جديد
        final newClient = ClientModel(
          name: _nameController.text.trim(),
          phone: _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
          createdAt: now,
          updatedAt: now,
        );

        context.read<ClientBloc>().add(AddClient(newClient));

        // إرسال إشعار إضافة عميل جديد
        try {
          await ClientNotificationService.notifyNewClient(newClient);
        } catch (e) {
          debugPrint('❌ خطأ في إرسال إشعار إضافة العميل: $e');
        }
      }
    } catch (e) {
      _showMessage('خطأ في حفظ العميل: $e');
    }
  }

  /// عرض رسالة
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 8),
            Text('نجح العملية'),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // إغلاق النافذة
              Navigator.pop(context); // العودة للصفحة السابقة
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 28),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: SizedBox(
          height: 300,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(message),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'ملاحظة:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 4),
                      Text('يمكنك إنشاء الحساب لاحقاً من صفحة تفاصيل العميل.'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // إغلاق النافذة
              Navigator.pop(context); // العودة للصفحة السابقة
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل العميل' : 'إضافة عميل جديد'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ClientBloc, ClientState>(
            listener: (context, state) {
              if (state is ClientOperationSuccess) {
                _showMessage(state.message);

                if (!_isEditing) {
                  // حفظ ID العميل المُنشأ
                  _lastCreatedClientId = state.clientId;
                  debugPrint('تم إنشاء العميل بـ ID: $_lastCreatedClientId');

                  // إضافة المزرعة للعميل الجديد
                  final clientId = state.clientId;
                  if (clientId != null &&
                      _farmNameController.text.trim().isNotEmpty) {
                    final farm = FarmModel(
                      clientId: clientId,
                      name: _farmNameController.text.trim(),
                      notes: _farmNotesController.text.trim().isEmpty
                          ? null
                          : _farmNotesController.text.trim(),
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    );

                    context.read<FarmBloc>().add(AddFarm(farm));
                  } else {
                    // إنشاء حساب العميل مباشرة إذا لم تكن هناك مزرعة
                    debugPrint('إنشاء حساب للعميل مباشرة: $clientId');
                    context.read<ClientAccountBloc>().add(
                          CreateClientAccount(clientId!),
                        );
                  }
                } else {
                  Navigator.pop(context);
                }
              } else if (state is ClientError) {
                _showMessage('خطأ: ${state.message}');
              }
            },
          ),
          BlocListener<FarmBloc, FarmState>(
            listener: (context, state) {
              if (state is FarmOperationSuccess) {
                _showMessage('تم إضافة المزرعة بنجاح');
                // إنشاء حساب العميل بعد إضافة المزرعة
                if (_lastCreatedClientId != null) {
                  debugPrint('إنشاء حساب للعميل: $_lastCreatedClientId');
                  context.read<ClientAccountBloc>().add(
                        CreateClientAccount(_lastCreatedClientId!),
                      );
                }
              } else if (state is FarmError) {
                _showMessage('خطأ في إضافة المزرعة: ${state.message}');
                // إنشاء حساب العميل حتى لو فشلت إضافة المزرعة
                if (_lastCreatedClientId != null) {
                  debugPrint(
                      'إنشاء حساب للعميل رغم فشل المزرعة: $_lastCreatedClientId');
                  context.read<ClientAccountBloc>().add(
                        CreateClientAccount(_lastCreatedClientId!),
                      );
                }
              }
            },
          ),
          BlocListener<ClientAccountBloc, ClientAccountState>(
            listener: (context, state) {
              if (state is ClientAccountOperationSuccess) {
                debugPrint('✅ تم إنشاء حساب العميل بنجاح');
                _showSuccessDialog('تم إنشاء العميل والحساب بنجاح!');
              } else if (state is ClientAccountError) {
                debugPrint('❌ خطأ في إنشاء الحساب: ${state.message}');
                _showErrorDialog(
                    'تم إنشاء العميل ولكن فشل في إنشاء الحساب', state.message);
              } else if (state is ClientAccountLoading) {
                debugPrint('⏳ جاري إنشاء حساب العميل...');
              }
            },
          ),
        ],
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم معلومات العميل
                _buildClientInfoSection(),
                const SizedBox(height: 24),

                // قسم المزرعة (للعملاء الجدد فقط)
                if (!_isEditing) ...[
                  _buildFarmSection(),
                  const SizedBox(height: 24),
                ],

                // أزرار الحفظ والإلغاء
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildClientInfoSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const Spacer(),
                if (!_isLoadingContacts)
                  IconButton(
                    onPressed: () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => ContactsPickerPage(
                            onContactSelected: (contact) {
                              _nameController.text = contact.name;
                              _phoneController.text = contact.phone;
                            },
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.contacts, color: Colors.blue),
                    tooltip: 'استيراد من جهات الاتصال',
                  )
                else
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // اسم العميل
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم العميل *',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم العميل';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // رقم الهاتف
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                prefixIcon: Icon(Icons.phone),
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),

            // ملاحظات
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFarmSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.agriculture, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'مزرعة العميل *',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // اسم المزرعة
            TextFormField(
              controller: _farmNameController,
              decoration: const InputDecoration(
                labelText: 'اسم المزرعة *',
                prefixIcon: Icon(Icons.agriculture),
                border: OutlineInputBorder(),
                hintText: 'مثال: مزرعة أحمد الرئيسية',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المزرعة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // ملاحظات المزرعة
            TextFormField(
              controller: _farmNotesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات المزرعة',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _saveClient,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(_isEditing ? 'حفظ التعديلات' : 'إضافة العميل'),
          ),
        ),
      ],
    );
  }
}

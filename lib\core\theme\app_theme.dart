import 'package:flutter/material.dart';

/// ثيم التطبيق الموحد
class AppTheme {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFF2196F3); // أزرق
  static const Color secondaryColor = Color(0xFF4CAF50); // أخضر
  static const Color accentColor = Color(0xFFFF9800); // برتقالي
  static const Color errorColor = Color(0xFFF44336); // أحمر
  static const Color warningColor = Color(0xFFFF5722); // برتقالي محمر
  static const Color successColor = Color(0xFF4CAF50); // أخضر
  static const Color infoColor = Color(0xFF2196F3); // أزرق

  // الألوان الرمادية
  static const Color lightGrey = Color(0xFFF5F5F5);
  static const Color mediumGrey = Color(0xFFBDBDBD);
  static const Color darkGrey = Color(0xFF757575);

  // لون الخلفية
  static const Color backgroundColor = Color(0xFFF8F9FA);

  // ألوان الوضع المظلم
  static const Color darkBackgroundColor = Color(0xFF121212);
  static const Color darkSurfaceColor = Color(0xFF1E1E1E);
  static const Color darkCardColor = Color(0xFF2D2D2D);
  static const Color darkTextColor = Color(0xFFE0E0E0);
  static const Color darkTextSecondaryColor = Color(0xFFB0B0B0);

  // ثيم التطبيق الرئيسي
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
        background: backgroundColor,
        surface: Colors.white,
        onBackground: Colors.black87,
        onSurface: Colors.black87,
      ),

      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),
      ),

      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
      ),

      // الكروت
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(8),
        color: Colors.white,
      ),

      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        filled: true,
        fillColor: Colors.white,
      ),

      // الخط
      fontFamily: 'Cairo',

      // الألوان العامة
      scaffoldBackgroundColor: backgroundColor,
      cardColor: Colors.white,
      dividerColor: mediumGrey,
    );
  }

  // ثيم التطبيق المظلم المحسن
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
        background: darkBackgroundColor,
        surface: darkSurfaceColor,
        onBackground: darkTextColor,
        onSurface: darkTextColor,
        primary: primaryColor,
        onPrimary: Colors.white,
        secondary: secondaryColor,
        onSecondary: Colors.white,
        error: errorColor,
        onError: Colors.white,
      ),

      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: darkSurfaceColor,
        foregroundColor: darkTextColor,
        elevation: 2,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: darkTextColor,
          fontFamily: 'Cairo',
        ),
      ),

      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
      ),

      // الكروت
      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(8),
        color: darkCardColor,
      ),

      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        filled: true,
        fillColor: darkCardColor,
        labelStyle: const TextStyle(color: darkTextSecondaryColor),
        hintStyle: const TextStyle(color: darkTextSecondaryColor),
      ),

      // الخط
      fontFamily: 'Cairo',

      // الألوان العامة
      scaffoldBackgroundColor: darkBackgroundColor,
      cardColor: darkCardColor,
      dividerColor: darkGrey,

      // ألوان النصوص
      textTheme: const TextTheme(
        bodyLarge: TextStyle(color: darkTextColor),
        bodyMedium: TextStyle(color: darkTextColor),
        bodySmall: TextStyle(color: darkTextSecondaryColor),
        titleLarge: TextStyle(color: darkTextColor),
        titleMedium: TextStyle(color: darkTextColor),
        titleSmall: TextStyle(color: darkTextColor),
      ),

      // ألوان الأيقونات
      iconTheme: const IconThemeData(
        color: darkTextColor,
      ),
    );
  }

  // ألوان الحالة
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'active':
        return successColor;
      case 'warning':
      case 'pending':
        return warningColor;
      case 'error':
      case 'failed':
      case 'cancelled':
        return errorColor;
      case 'info':
      case 'processing':
        return infoColor;
      default:
        return mediumGrey;
    }
  }

  // أيقونات الحالة
  static IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
        return Icons.check_circle;
      case 'warning':
      case 'pending':
        return Icons.warning;
      case 'error':
      case 'failed':
        return Icons.error;
      case 'info':
      case 'processing':
        return Icons.info;
      case 'active':
        return Icons.check_circle_outline;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  // تدرجات الألوان
  static LinearGradient get primaryGradient {
    return const LinearGradient(
      colors: [primaryColor, Color(0xFF1976D2)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  static LinearGradient get successGradient {
    return const LinearGradient(
      colors: [successColor, Color(0xFF388E3C)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  static LinearGradient get warningGradient {
    return const LinearGradient(
      colors: [warningColor, Color(0xFFE64A19)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  static LinearGradient get errorGradient {
    return const LinearGradient(
      colors: [errorColor, Color(0xFFD32F2F)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  // ظلال مخصصة
  static List<BoxShadow> get cardShadow {
    return [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.1),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ];
  }

  static List<BoxShadow> get buttonShadow {
    return [
      BoxShadow(
        color: primaryColor.withValues(alpha: 0.3),
        blurRadius: 8,
        offset: const Offset(0, 4),
      ),
    ];
  }

  // أنماط النصوص
  static const TextStyle headingLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Colors.black87,
  );

  static const TextStyle headingMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Colors.black87,
  );

  static const TextStyle headingSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: Colors.black87,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: Colors.black87,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: Colors.black87,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: Colors.black54,
  );

  static const TextStyle caption = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.normal,
    color: Colors.black54,
  );

  // أنماط الأزرار
  static ButtonStyle get primaryButtonStyle {
    return ElevatedButton.styleFrom(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 2,
    );
  }

  static ButtonStyle get secondaryButtonStyle {
    return OutlinedButton.styleFrom(
      foregroundColor: primaryColor,
      side: const BorderSide(color: primaryColor),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  // أنماط الحاويات
  static BoxDecoration get cardDecoration {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: cardShadow,
    );
  }

  static BoxDecoration get primaryContainerDecoration {
    return BoxDecoration(
      gradient: primaryGradient,
      borderRadius: BorderRadius.circular(12),
      boxShadow: cardShadow,
    );
  }

  // دوال مساعدة للأرصدة
  /// الحصول على لون الرصيد حسب القيمة
  static Color getBalanceColor(double balance) {
    if (balance < 0) {
      return warningColor; // برتقالي تحذيري للأرصدة السالبة
    } else if (balance == 0) {
      return mediumGrey; // رمادي للرصيد الصفر
    } else {
      return successColor; // أخضر للأرصدة الموجبة
    }
  }

  /// الحصول على أيقونة الرصيد حسب القيمة
  static IconData getBalanceIcon(double balance) {
    if (balance < 0) {
      return Icons.trending_down; // سهم للأسفل للأرصدة السالبة
    } else if (balance == 0) {
      return Icons.remove; // خط أفقي للرصيد الصفر
    } else {
      return Icons.trending_up; // سهم للأعلى للأرصدة الموجبة
    }
  }

  /// تنسيق عرض الرصيد مع الرمز المناسب
  static String formatBalance(double balance, String unit) {
    String sign = balance < 0 ? '-' : '';
    String value = balance.abs().toStringAsFixed(2);
    return '$sign$value $unit';
  }

  /// الحصول على نمط النص للرصيد
  static TextStyle getBalanceTextStyle(double balance, {double fontSize = 16}) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.bold,
      color: getBalanceColor(balance),
    );
  }

  /// الحصول على رسالة حالة الرصيد
  static String getBalanceStatusMessage(double balance, String type) {
    if (balance < 0) {
      return 'رصيد $type سالب';
    } else if (balance == 0) {
      return 'رصيد $type صفر';
    } else {
      return 'رصيد $type متاح';
    }
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/services/filter_preferences_service.dart';
import 'package:untitled/core/services/date_validation_service.dart';
import 'package:untitled/core/services/enhanced_report_data_service.dart';
import 'package:untitled/core/services/report_export_service.dart';
import 'package:untitled/core/services/database_query_optimizer.dart';
import 'package:untitled/core/services/real_data_manager.dart';
import 'package:untitled/core/services/advanced_export_service.dart';
import 'package:untitled/core/utils/interactive_stats_helper.dart';
import 'package:untitled/core/services/data_repair_service.dart';
import 'package:untitled/core/services/report_display_enhancer.dart';
import 'fullscreen_report_page.dart';

/// صفحة التقارير المخصصة المحسنة
class CustomReportsPage extends StatefulWidget {
  const CustomReportsPage({super.key});

  @override
  State<CustomReportsPage> createState() => _CustomReportsPageState();
}

class _CustomReportsPageState extends State<CustomReportsPage> {
  // البيانات الأساسية
  List<IrrigationModel> _irrigations = [];
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];

  // حالة التحميل
  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 4;

  // إعدادات التقرير المحسنة
  String _reportType = 'detailed'; // detailed, comparison, summary
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // فلاتر محسنة
  String? _selectedClientId;
  String? _selectedFarmId;
  String _searchQuery = '';
  String _selectedTypeFilter = '';
  String _selectedStatusFilter = '';

  bool _showAdvancedFilters = false;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _minAmountController = TextEditingController();
  final TextEditingController _maxAmountController = TextEditingController();

  // بيانات التقرير
  final List<Map<String, dynamic>> _reportData = [];
  bool _reportGenerated = false;

  // تحديث دوري
  Timer? _refreshTimer;

  // حالة التحقق من البيانات
  bool _dataValidated = false;
  Map<String, dynamic>? _dataValidationResults;

  // متغيرات إدارة البيانات الحقيقية
  bool _useRealisticData = true; // استخدام البيانات الحقيقية دائماً
  bool _dataInitialized = false;

  @override
  void initState() {
    super.initState();
    _loadSavedFilters();
    _initializeRealisticData();

    // إعداد تحديث دوري للبيانات (كل 5 دقائق)
    _setupPeriodicRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  /// إعداد التحديث الدوري
  void _setupPeriodicRefresh() {
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (mounted && !_isLoading) {
        debugPrint('🔄 تحديث دوري للبيانات...');
        _loadAllData();
      }
    });
  }

  /// تحميل الفلاتر المحفوظة
  Future<void> _loadSavedFilters() async {
    try {
      final savedFilters = await FilterPreferencesService.getReportDateFilter();
      if (mounted) {
        setState(() {
          _startDate = savedFilters['startDate'] ?? _startDate;
          _endDate = savedFilters['endDate'] ?? _endDate;
        });
      }

      // تحميل إعدادات التقرير الأخرى
      await _loadReportSettings();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفلاتر المحفوظة: $e');
    }
  }

  /// تحميل إعدادات التقرير
  Future<void> _loadReportSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final savedReportType = prefs.getString('report_type');
      final savedClientId = prefs.getString('report_client_id');
      final savedFarmId = prefs.getString('report_farm_id');

      if (mounted) {
        setState(() {
          if (savedReportType != null) {
            _reportType = savedReportType;
          }
          _selectedClientId = savedClientId;
          _selectedFarmId = savedFarmId;
        });
      }

      debugPrint('✅ تم تحميل إعدادات التقرير المحفوظة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات التقرير: $e');
    }
  }

  /// حفظ إعدادات التقرير
  Future<void> _saveReportSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('report_type', _reportType);

      if (_selectedClientId != null) {
        await prefs.setString('report_client_id', _selectedClientId!);
      } else {
        await prefs.remove('report_client_id');
      }

      if (_selectedFarmId != null) {
        await prefs.setString('report_farm_id', _selectedFarmId!);
      } else {
        await prefs.remove('report_farm_id');
      }

      debugPrint('✅ تم حفظ إعدادات التقرير');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات التقرير: $e');
    }
  }

  /// مسح جميع الفلاتر
  Future<void> _clearAllFilters() async {
    try {
      // إظهار حوار تأكيد
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              Padding(padding: const EdgeInsets.only(left: 8)),
              Text('تأكيد المسح'),
            ],
          ),
          content: const Text(
            'هل أنت متأكد من أنك تريد مسح جميع الفلاتر والإعدادات؟\n'
            'سيتم إعادة تعيين جميع الفلاتر إلى القيم الافتراضية.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('مسح', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // مسح الإعدادات المحفوظة
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('report_type');
        await prefs.remove('report_client_id');
        await prefs.remove('report_farm_id');

        // مسح فلاتر التواريخ
        await FilterPreferencesService.clearAllFilters();

        // إعادة تعيين القيم
        setState(() {
          _reportType = 'summary';
          _selectedClientId = null;
          _selectedFarmId = null;
          _searchQuery = '';
          _searchController.clear();
          _startDate = DateTime.now().subtract(const Duration(days: 30));
          _endDate = DateTime.now();
          _reportGenerated = false;
          _reportData.clear();
        });

        _showSuccessMessage('تم مسح جميع الفلاتر بنجاح!');
        debugPrint('✅ تم مسح جميع فلاتر التقارير');
      }
    } catch (e) {
      debugPrint('❌ خطأ في مسح الفلاتر: $e');
      _showErrorMessage('حدث خطأ أثناء مسح الفلاتر');
    }
  }

  /// تحميل جميع البيانات
  void _loadAllData() {
    if (_isLoading) {
      debugPrint('⚠️ تحميل البيانات قيد التشغيل بالفعل...');
      return;
    }

    setState(() {
      _isLoading = true;
      _loadedCount = 0;
      _reportGenerated = false;
    });

    debugPrint('🔄 بدء تحميل البيانات...');

    // تحميل البيانات مع معالجة الأخطاء
    try {
      context.read<IrrigationBloc>().add(const LoadIrrigations());
      context.read<PaymentBloc>().add(const LoadPayments());
      context.read<ClientBloc>().add(const LoadClients());
      context.read<FarmBloc>().add(const LoadFarms());
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage('حدث خطأ في تحميل البيانات: $e');
    }
  }

  /// التحقق من اكتمال تحميل البيانات
  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });

      // التحقق من صحة البيانات
      _validateDataIntegrity();
    }
  }

  /// التحقق من صحة ودقة البيانات
  Future<void> _validateDataIntegrity() async {
    if (_dataValidated) return;

    try {
      debugPrint('🔍 بدء التحقق من صحة البيانات...');

      final validationResults =
          await EnhancedReportDataService.validateDataIntegrity(
        irrigations: _irrigations,
        payments: _payments,
        clients: _clients,
        farms: _farms,
      );

      setState(() {
        _dataValidationResults = validationResults;
        _dataValidated = true;
      });

      // عرض تقرير صحة البيانات في وضع التصحيح
      if (kDebugMode && validationResults.isNotEmpty) {
        final healthReport = EnhancedReportDataService.generateDataHealthReport(
            validationResults);
        debugPrint('📊 تقرير صحة البيانات:\n$healthReport');
      }

      // إظهار تحذيرات إذا وجدت مشاكل
      _checkForDataIssues(validationResults);
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من صحة البيانات: $e');
    }
  }

  /// فحص مشاكل البيانات وإظهار تحذيرات
  void _checkForDataIssues(Map<String, dynamic> validationResults) {
    int totalIssues = 0;

    for (final entry in validationResults.entries) {
      if (entry.key == 'summary' || entry.key == 'error') continue;

      final data = entry.value as Map<String, dynamic>;
      final issues = data['issues'] as List<dynamic>;
      totalIssues += issues.length;
    }

    if (totalIssues > 0) {
      _showDataIssuesWarning(totalIssues);
    }
  }

  /// إظهار تحذير مشاكل البيانات
  void _showDataIssuesWarning(int issuesCount) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.warning, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                    'تم اكتشاف $issuesCount مشكلة في البيانات. اضغط لعرض التفاصيل.'),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'عرض',
            textColor: Colors.white,
            onPressed: _showDataHealthReport,
          ),
        ),
      );
    }
  }

  /// عرض تقرير صحة البيانات
  void _showDataHealthReport() {
    if (_dataValidationResults == null) return;

    final healthReport = EnhancedReportDataService.generateDataHealthReport(
        _dataValidationResults!);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.health_and_safety, color: Colors.blue),
            const SizedBox(width: 8),
            const Text('تقرير صحة البيانات'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              healthReport,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _cleanAndCorrectData();
            },
            child: const Text('تنظيف البيانات'),
          ),
        ],
      ),
    );
  }

  /// تنظيف وتصحيح البيانات
  Future<void> _cleanAndCorrectData() async {
    try {
      _showLoadingMessage('جاري تنظيف البيانات...');

      final cleanedData = EnhancedReportDataService.cleanAndCorrectData(
        irrigations: _irrigations,
        payments: _payments,
        clients: _clients,
        farms: _farms,
      );

      setState(() {
        _irrigations =
            cleanedData['irrigations']?.cast<IrrigationModel>() ?? [];
        _payments = cleanedData['payments']?.cast<PaymentModel>() ?? [];
        _clients = cleanedData['clients']?.cast<ClientModel>() ?? [];
        _farms = cleanedData['farms']?.cast<FarmModel>() ?? [];
        _dataValidated = false; // إعادة التحقق
      });

      // إعادة التحقق من البيانات
      await _validateDataIntegrity();

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showSuccessMessage('تم تنظيف البيانات بنجاح!');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showErrorMessage('حدث خطأ أثناء تنظيف البيانات: $e');
      }
    }
  }

  /// التحقق من وجود التاريخ في النطاق المحدد
  bool _isDateInRange(DateTime date) {
    final startOfDay =
        DateTime(_startDate.year, _startDate.month, _startDate.day);
    final endOfDay =
        DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);

    return date.isAfter(startOfDay.subtract(const Duration(seconds: 1))) &&
        date.isBefore(endOfDay.add(const Duration(seconds: 1)));
  }

  /// الحصول على العميل بالمعرف
  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على المزرعة بالمعرف
  FarmModel? _getFarmById(int farmId) {
    try {
      return _farms.firstWhere((farm) => farm.id == farmId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    // مؤشر حالة البيانات
                    if (_useRealisticData) _buildDataStatusIndicator(),

                    // لوحة الإعدادات
                    Container(
                      color: Colors.white,
                      child: _buildSettingsPanel(),
                    ),
                    const Divider(height: 1),
                    // منطقة عرض التقرير
                    SizedBox(
                      height: MediaQuery.of(context).size.height - 300,
                      child: _reportGenerated
                          ? _buildReportArea()
                          : _buildEmptyState(),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'التقارير المخصصة',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.clear_all),
          onPressed: _clearAllFilters,
          tooltip: 'مسح جميع الفلاتر',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        if (_reportGenerated) ...[
          IconButton(
            icon: const Icon(Icons.fullscreen),
            onPressed: _showFullscreenReport,
            tooltip: 'عرض بملء الشاشة',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: 'المزيد من الخيارات',
            onSelected: (value) {
              switch (value) {
                case 'print':
                  _printReport();
                  break;
                case 'stats':
                  _showInteractiveStats();
                  break;
                case 'health':
                  _showDataHealthReport();
                  break;
                case 'repair':
                  _performDataRepair();
                  break;
                case 'regenerate':
                  _regenerateRealisticData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'print',
                child: ListTile(
                  leading: Icon(Icons.print),
                  title: Text('طباعة التقرير'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'stats',
                child: ListTile(
                  leading: Icon(Icons.analytics_outlined),
                  title: Text('إحصائيات تفاعلية'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              if (_dataValidationResults != null)
                const PopupMenuItem(
                  value: 'health',
                  child: ListTile(
                    leading: Icon(Icons.health_and_safety),
                    title: Text('تقرير صحة البيانات'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              const PopupMenuItem(
                value: 'repair',
                child: ListTile(
                  leading: Icon(Icons.build_circle_outlined),
                  title: Text('إصلاح البيانات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'regenerate',
                child: ListTile(
                  leading: Icon(Icons.refresh_outlined),
                  title: Text('إعادة إنشاء البيانات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ] else ...[
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showDetailedDataStatistics,
            tooltip: 'إحصائيات البيانات',
          ),
        ],
      ],
    );
  }

  /// بناء مستمعي BLoC
  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          } else if (state is IrrigationError) {
            _showErrorMessage('خطأ في تحميل بيانات التسقيات: ${state.message}');
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          } else if (state is PaymentError) {
            _showErrorMessage(
                'خطأ في تحميل بيانات المدفوعات: ${state.message}');
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          } else if (state is ClientError) {
            _showErrorMessage('خطأ في تحميل بيانات العملاء: ${state.message}');
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<FarmBloc, FarmState>(
        listener: (context, state) {
          if (state is FarmsLoaded) {
            setState(() => _farms = state.farms);
            _checkDataLoaded();
          } else if (state is FarmError) {
            _showErrorMessage('خطأ في تحميل بيانات المزارع: ${state.message}');
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  /// بناء لوحة الإعدادات
  Widget _buildSettingsPanel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Icon(Icons.tune, color: AppTheme.primaryColor),
              SizedBox(width: 8),
              Text(
                'إعدادات التقرير',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // نوع التقرير
          _buildReportTypeSelector(),
          const SizedBox(height: 16),

          // معلومات البيانات
          _buildDataInfoSection(),
          const SizedBox(height: 16),

          // الفترة الزمنية
          _buildDateRangeSelector(),
          const SizedBox(height: 16),

          // الفلاتر
          _buildFiltersSection(),
          const SizedBox(height: 16),

          // زر إنشاء التقرير
          _buildGenerateButton(),
        ],
      ),
    );
  }

  /// بناء محدد نوع التقرير
  Widget _buildReportTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع التقرير',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildReportTypeCard(
                'summary',
                'تقرير ملخص',
                'إحصائيات سريعة',
                Icons.summarize,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildReportTypeCard(
                'detailed',
                'تقرير مفصل',
                'جميع العمليات',
                Icons.list_alt,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildReportTypeCard(
                'comparison',
                'تقرير مقارن',
                'مقارنة الفترات',
                Icons.compare_arrows,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة نوع التقرير
  Widget _buildReportTypeCard(
      String value, String title, String subtitle, IconData icon) {
    final isSelected = _reportType == value;
    return InkWell(
      onTap: () {
        setState(() => _reportType = value);
        _saveReportSettings();
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withAlpha((0.1 * 255).toInt())
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? AppTheme.primaryColor : Colors.grey.shade600,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: isSelected ? AppTheme.primaryColor : Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محدد الفترة الزمنية
  Widget _buildDateRangeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الفترة الزمنية',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateField('تاريخ البداية', _startDate, true),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateField('تاريخ النهاية', _endDate, false),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
              const SizedBox(width: 8),
              Text(
                'المدة: ${_endDate.difference(_startDate).inDays + 1} يوم',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء حقل التاريخ
  Widget _buildDateField(String label, DateTime date, bool isStartDate) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        InkWell(
          onTap: () => _selectDate(isStartDate),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[50],
            ),
            child: Row(
              children: [
                const Icon(Icons.date_range,
                    size: 18, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  DateFormat('dd/MM/yyyy').format(date),
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء قسم الفلاتر المحسن
  Widget _buildFiltersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.filter_list,
                color: AppTheme.primaryColor, size: 20),
            const SizedBox(width: 8),
            const Text(
              'الفلاتر',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _showAdvancedFilters = !_showAdvancedFilters;
                });
              },
              icon: Icon(
                _showAdvancedFilters ? Icons.expand_less : Icons.expand_more,
                size: 18,
              ),
              label: Text(
                _showAdvancedFilters ? 'إخفاء المتقدم' : 'فلاتر متقدمة',
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // الفلاتر الأساسية
        Row(
          children: [
            Expanded(
              child: _buildClientDropdown(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildFarmDropdown(),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildSearchField(),

        // الفلاتر المتقدمة
        if (_showAdvancedFilters) ...[
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 12),
          _buildAdvancedFilters(),
        ],
      ],
    );
  }

  /// بناء الفلاتر المتقدمة
  Widget _buildAdvancedFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.tune, color: AppTheme.primaryColor, size: 18),
            const SizedBox(width: 6),
            Text(
              'فلاتر متقدمة',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // فلتر النوع والحالة
        Row(
          children: [
            Expanded(
              child: _buildTypeFilter(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatusFilter(),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // فلتر المبلغ
        Row(
          children: [
            Expanded(
              child: _buildAmountRangeFilter(),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // أزرار إعادة تعيين وتطبيق
        Row(
          children: [
            TextButton.icon(
              onPressed: _resetAdvancedFilters,
              icon: const Icon(Icons.clear, size: 16),
              label: const Text('إعادة تعيين'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade600,
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton.icon(
              onPressed: _applyAdvancedFilters,
              icon: const Icon(Icons.check, size: 16),
              label: const Text('تطبيق الفلاتر'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // زر إنشاء البيانات التجريبية
        Container(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _createSampleData,
            icon: const Icon(Icons.data_usage, size: 18),
            label: const Text('إنشاء بيانات تجريبية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء فلتر النوع
  Widget _buildTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع العملية',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[50],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedTypeFilter.isEmpty ? null : _selectedTypeFilter,
              hint: const Text('جميع الأنواع', style: TextStyle(fontSize: 12)),
              isExpanded: true,
              items: const [
                DropdownMenuItem<String>(
                  value: null,
                  child: Text('جميع الأنواع', style: TextStyle(fontSize: 12)),
                ),
                DropdownMenuItem<String>(
                  value: 'irrigation',
                  child: Text('التسقيات', style: TextStyle(fontSize: 12)),
                ),
                DropdownMenuItem<String>(
                  value: 'payment',
                  child: Text('المدفوعات', style: TextStyle(fontSize: 12)),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedTypeFilter = value ?? '';
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء فلتر الحالة
  Widget _buildStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الحالة',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[50],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value:
                  _selectedStatusFilter.isEmpty ? null : _selectedStatusFilter,
              hint: const Text('جميع الحالات', style: TextStyle(fontSize: 12)),
              isExpanded: true,
              items: const [
                DropdownMenuItem<String>(
                  value: null,
                  child: Text('جميع الحالات', style: TextStyle(fontSize: 12)),
                ),
                DropdownMenuItem<String>(
                  value: 'completed',
                  child: Text('مكتملة', style: TextStyle(fontSize: 12)),
                ),
                DropdownMenuItem<String>(
                  value: 'pending',
                  child: Text('معلقة', style: TextStyle(fontSize: 12)),
                ),
                DropdownMenuItem<String>(
                  value: 'cancelled',
                  child: Text('ملغية', style: TextStyle(fontSize: 12)),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatusFilter = value ?? '';
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء فلتر نطاق المبلغ
  Widget _buildAmountRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نطاق المبلغ (ريال)',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _minAmountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'من',
                  hintStyle: const TextStyle(fontSize: 12),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
                style: const TextStyle(fontSize: 12),
                onChanged: (value) {
                  // يمكن إضافة منطق الفلترة هنا لاحقاً
                },
              ),
            ),
            const SizedBox(width: 8),
            const Text('إلى', style: TextStyle(fontSize: 12)),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _maxAmountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'إلى',
                  hintStyle: const TextStyle(fontSize: 12),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
                style: const TextStyle(fontSize: 12),
                onChanged: (value) {
                  // يمكن إضافة منطق الفلترة هنا لاحقاً
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// إعادة تعيين الفلاتر المتقدمة
  void _resetAdvancedFilters() {
    setState(() {
      _selectedTypeFilter = '';
      _selectedStatusFilter = '';
      _minAmountController.clear();
      _maxAmountController.clear();
    });
    _showSuccessMessage('تم إعادة تعيين الفلاتر المتقدمة');
  }

  /// تطبيق الفلاتر المتقدمة
  void _applyAdvancedFilters() {
    if (_reportGenerated) {
      _generateReport();
      _showSuccessMessage('تم تطبيق الفلاتر المتقدمة');
    } else {
      _showErrorMessage('يجب إنشاء التقرير أولاً');
    }
  }

  /// بناء قائمة العملاء المنسدلة
  Widget _buildClientDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العميل',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[50],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedClientId,
              hint: const Text('جميع العملاء'),
              isExpanded: true,
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('جميع العملاء'),
                ),
                ..._clients.map((client) => DropdownMenuItem<String>(
                      value: client.id.toString(),
                      child: Text(client.name),
                    )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedClientId = value;
                });
                _saveReportSettings();
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء قائمة المزارع المنسدلة
  Widget _buildFarmDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المزرعة',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[50],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedFarmId,
              hint: const Text('جميع المزارع'),
              isExpanded: true,
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('جميع المزارع'),
                ),
                ..._farms.map((farm) => DropdownMenuItem<String>(
                      value: farm.id.toString(),
                      child: Text(farm.name),
                    )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedFarmId = value;
                });
                _saveReportSettings();
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء حقل البحث
  Widget _buildSearchField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البحث',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'البحث في البيانات...',
            prefixIcon: const Icon(Icons.search, color: AppTheme.primaryColor),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                      });
                    },
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.primaryColor),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
      ],
    );
  }

  /// بناء زر إنشاء التقرير
  Widget _buildGenerateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _generateReport,
        icon: const Icon(Icons.analytics, color: Colors.white),
        label: const Text(
          'إنشاء التقرير',
          style: TextStyle(
              color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// بناء منطقة عرض التقرير
  Widget _buildReportArea() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // عنوان التقرير
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha((0.1 * 255).toInt()),
              border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
            ),
            child: Row(
              children: [
                Icon(
                  _getReportIcon(),
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getEnhancedReportTitle(),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      Text(
                        'من ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${_reportData.length} عنصر',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // محتوى التقرير
          Expanded(
            child: _reportData.isEmpty
                ? _buildNoDataWidget()
                : _buildReportContent(),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى التقرير
  Widget _buildReportContent() {
    switch (_reportType) {
      case 'summary':
        return _buildSummaryReport();
      case 'comparison':
        return _buildComparisonReport();
      default:
        return _buildDetailedReport();
    }
  }

  /// بناء التقرير الملخص
  Widget _buildSummaryReport() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _reportData.length,
      itemBuilder: (context, index) {
        final item = _reportData[index];
        final color = item['color'] as Color? ?? AppTheme.primaryColor;
        final icon = item['icon'] as IconData? ?? Icons.analytics;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [color.withAlpha((0.1 * 255).toInt()), Colors.white],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: color.withAlpha((0.2 * 255).toInt()),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(icon, color: color, size: 24),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          item['title'] ?? '',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  _buildEnhancedSummaryStats(item, color),

                  // إضافة تفاصيل إضافية إذا كانت متوفرة
                  if (item['details'] != null) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    _buildAdditionalDetails(item['details'], color),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء إحصائيات الملخص المحسنة
  Widget _buildEnhancedSummaryStats(Map<String, dynamic> item, Color color) {
    List<Widget> stats = [];

    // إحصائيات التسقيات
    if (item['total_irrigations'] != null) {
      stats.addAll([
        _buildEnhancedStatItem('عدد التسقيات', '${item['total_irrigations']}',
            Icons.water_drop, color),
        _buildEnhancedStatItem(
            'إجمالي التكلفة',
            '${ReportDisplayEnhancer.formatNumber(item['total_cost'] ?? 0)} ريال',
            Icons.attach_money,
            color),
        _buildEnhancedStatItem(
            'إجمالي الديزل',
            '${ReportDisplayEnhancer.formatNumber(item['total_diesel'] ?? 0)} لتر',
            Icons.local_gas_station,
            color),
        _buildEnhancedStatItem(
            'إجمالي الساعات',
            '${ReportDisplayEnhancer.formatNumber(item['total_hours'] ?? 0)} ساعة',
            Icons.access_time,
            color),
        _buildEnhancedStatItem(
            'متوسط التكلفة',
            '${ReportDisplayEnhancer.formatNumber(item['average_cost'] ?? 0)} ريال',
            Icons.trending_up,
            color),
      ]);
    }

    // إحصائيات المدفوعات
    if (item['total_payments'] != null) {
      stats.addAll([
        _buildEnhancedStatItem(
            'عدد المدفوعات', '${item['total_payments']}', Icons.payment, color),
        _buildEnhancedStatItem(
            'إجمالي المبلغ',
            '${ReportDisplayEnhancer.formatNumber(item['total_amount'] ?? 0)} ريال',
            Icons.account_balance_wallet,
            color),
        _buildEnhancedStatItem(
            'متوسط الدفعة',
            '${ReportDisplayEnhancer.formatNumber(item['average_amount'] ?? 0)} ريال',
            Icons.trending_up,
            color),
      ]);
    }

    // إحصائيات العملاء
    if (item['client_name'] != null) {
      stats.addAll([
        _buildEnhancedStatItem(
            'العميل', '${item['client_name']}', Icons.person, color),
        _buildEnhancedStatItem('عدد التسقيات', '${item['irrigations_count']}',
            Icons.water_drop, color),
        _buildEnhancedStatItem(
            'إجمالي التكلفة',
            '${ReportDisplayEnhancer.formatNumber(item['total_cost'] ?? 0)} ريال',
            Icons.attach_money,
            color),
        _buildEnhancedStatItem(
            'إجمالي الديزل',
            '${ReportDisplayEnhancer.formatNumber(item['total_diesel'] ?? 0)} لتر',
            Icons.local_gas_station,
            color),
        _buildEnhancedStatItem(
            'إجمالي المدفوعات',
            '${ReportDisplayEnhancer.formatNumber(item['total_payments'] ?? 0)} ريال',
            Icons.payment,
            color),
        _buildEnhancedStatItem(
          'الرصيد',
          '${ReportDisplayEnhancer.formatNumber(item['balance'] ?? 0)} ريال',
          item['balance'] != null && item['balance'] >= 0
              ? Icons.check_circle
              : Icons.warning,
          item['balance'] != null && item['balance'] >= 0
              ? Colors.green
              : Colors.red,
        ),
      ]);
    }

    // إحصائيات المزارع
    if (item['farm_name'] != null) {
      stats.addAll([
        _buildEnhancedStatItem(
            'المزرعة', '${item['farm_name']}', Icons.agriculture, color),
        _buildEnhancedStatItem('عدد التسقيات', '${item['irrigations_count']}',
            Icons.water_drop, color),
        _buildEnhancedStatItem(
            'إجمالي التكلفة',
            '${ReportDisplayEnhancer.formatNumber(item['total_cost'] ?? 0)} ريال',
            Icons.attach_money,
            color),
        _buildEnhancedStatItem(
            'إجمالي الديزل',
            '${ReportDisplayEnhancer.formatNumber(item['total_diesel'] ?? 0)} لتر',
            Icons.local_gas_station,
            color),
        _buildEnhancedStatItem(
            'إجمالي الساعات',
            '${ReportDisplayEnhancer.formatNumber(item['total_duration'] ?? 0)} ساعة',
            Icons.access_time,
            color),
      ]);
    }

    // إحصائيات يومية
    if (item['date'] != null && item['title'].toString().contains('أفضل يوم')) {
      stats.addAll([
        _buildEnhancedStatItem(
            'التاريخ', '${item['date']}', Icons.calendar_today, color),
        _buildEnhancedStatItem('عدد التسقيات', '${item['irrigations_count']}',
            Icons.water_drop, color),
        _buildEnhancedStatItem(
            'إجمالي التكلفة',
            '${ReportDisplayEnhancer.formatNumber(item['total_cost'] ?? 0)} ريال',
            Icons.attach_money,
            color),
        _buildEnhancedStatItem(
            'إجمالي الديزل',
            '${ReportDisplayEnhancer.formatNumber(item['total_diesel'] ?? 0)} لتر',
            Icons.local_gas_station,
            color),
        _buildEnhancedStatItem(
            'إجمالي الساعات',
            '${ReportDisplayEnhancer.formatNumber(item['total_duration'] ?? 0)} ساعة',
            Icons.access_time,
            color),
      ]);
    }

    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: stats,
    );
  }

  /// بناء عنصر إحصائية محسن
  Widget _buildEnhancedStatItem(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withAlpha((0.1 * 255).toInt()),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha((0.3 * 255).toInt())),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء التفاصيل الإضافية
  Widget _buildAdditionalDetails(Map<String, dynamic> details, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: details.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              Text(
                '${entry.key}: ',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
              Expanded(
                child: Text(
                  entry.value.toString(),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// بناء التقرير المقارن
  Widget _buildComparisonReport() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _reportData.length,
      itemBuilder: (context, index) {
        final item = _reportData[index];
        final color = item['color'] as Color? ?? AppTheme.primaryColor;
        final icon = item['icon'] as IconData? ?? Icons.compare_arrows;
        final trend = item['trend'] as String? ?? '';

        // تحديد لون الاتجاه
        Color trendColor = Colors.grey;
        IconData trendIcon = Icons.trending_flat;

        if (trend.contains('تحسن') || trend.contains('ارتفاع')) {
          trendColor = Colors.green;
          trendIcon = Icons.trending_up;
        } else if (trend.contains('انخفاض')) {
          trendColor = Colors.red;
          trendIcon = Icons.trending_down;
        }

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [color.withAlpha((0.1 * 255).toInt()), Colors.white],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: color.withAlpha((0.2 * 255).toInt()),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(icon, color: color, size: 24),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          item['title'] ?? '',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: trendColor.withAlpha((0.2 * 255).toInt()),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(trendIcon, color: trendColor, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              '${item['count_change_percent']?.toStringAsFixed(1) ?? '0'}%',
                              style: TextStyle(
                                color: trendColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // مقارنة البيانات - تم حذفها لعدم وجود الدوال اللازمة

                  const SizedBox(height: 16),

                  // الملاحظات
                  if (item['notes'] != null &&
                      item['notes'].toString().isNotEmpty) ...[
                    const Text(
                      'الملاحظات:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Text(
                        item['notes'].toString(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء التقرير المفصل
  Widget _buildDetailedReport() {
    if (_reportData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للعرض',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return Column(
      children: [
        // إحصائيات سريعة
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue[50]!, Colors.white],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: _buildDetailedQuickStats(),
        ),

        // قائمة العناصر المحسنة
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _reportData.length,
            itemBuilder: (context, index) {
              final item = _reportData[index];
              return _buildEnhancedDetailedItem(item, index);
            },
          ),
        ),
      ],
    );
  }

  /// بناء الإحصائيات السريعة للتقرير المفصل
  Widget _buildDetailedQuickStats() {
    final irrigations =
        _reportData.where((item) => item['type'] == 'irrigation').toList();
    final payments =
        _reportData.where((item) => item['type'] == 'payment').toList();

    final totalIrrigations = irrigations.length;
    final totalPayments = payments.length;
    final totalIrrigationCost =
        irrigations.fold(0.0, (sum, item) => sum + (item['amount'] ?? 0));
    final totalPaymentAmount =
        payments.fold(0.0, (sum, item) => sum + (item['amount'] ?? 0));

    return Row(
      children: [
        Expanded(
          child: _buildQuickStatCard(
            'التسقيات',
            '$totalIrrigations',
            '${ReportDisplayEnhancer.formatNumber(totalIrrigationCost)} ريال',
            Colors.blue,
            Icons.water_drop,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickStatCard(
            'المدفوعات',
            '$totalPayments',
            '${ReportDisplayEnhancer.formatNumber(totalPaymentAmount)} ريال',
            Colors.green,
            Icons.payment,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildQuickStatCard(
            'الرصيد',
            '',
            '${ReportDisplayEnhancer.formatNumber(totalPaymentAmount - totalIrrigationCost)} ريال',
            totalPaymentAmount >= totalIrrigationCost
                ? Colors.green
                : Colors.red,
            totalPaymentAmount >= totalIrrigationCost
                ? Icons.check_circle
                : Icons.warning,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية سريعة
  Widget _buildQuickStatCard(
      String title, String count, String amount, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha((0.1 * 255).toInt()),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha((0.3 * 255).toInt())),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (count.isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              count,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 2),
          Text(
            amount,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر مفصل محسن مع إمكانية عرض التفاصيل
  Widget _buildEnhancedDetailedItem(Map<String, dynamic> item, int index) {
    final isIrrigation = item['type'] == 'irrigation';
    final color =
        item['color'] as Color? ?? (isIrrigation ? Colors.blue : Colors.green);
    final icon = item['icon'] as IconData? ??
        (isIrrigation ? Icons.water_drop : Icons.payment);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border(
            right: BorderSide(color: color, width: 4),
          ),
        ),
        child: ExpansionTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha((0.2 * 255).toInt()),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          title: Text(
            item['title'] ?? '',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'التاريخ: ${item['date']}',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              if (item['time'] != null)
                Text(
                  'الوقت: ${item['time']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
            ],
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${ReportDisplayEnhancer.formatNumber(item['amount'] ?? 0)} ريال',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              if (isIrrigation && item['duration'] != null)
                Text(
                  '${item['duration']} د',
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                ),
            ],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // تفاصيل إضافية
                  if (item['details'] != null) ...[
                    const Text(
                      'التفاصيل:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...(item['details'] as Map<String, dynamic>).entries.map(
                          (entry) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Row(
                              children: [
                                Text(
                                  '${entry.key}: ',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    entry.value.toString(),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade700,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    const SizedBox(height: 12),
                  ],

                  // الملاحظات
                  if (item['notes'] != null &&
                      item['notes'].toString().isNotEmpty) ...[
                    const Text(
                      'الملاحظات:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Text(
                        item['notes'].toString(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Container(
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لم يتم إنشاء تقرير بعد',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اختر إعدادات التقرير واضغط على "إنشاء التقرير"',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عدم وجود بيانات
  Widget _buildNoDataWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات في الفترة المحددة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفترة الزمنية أو الفلاتر',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
    );

    if (picked != null) {
      // التحقق من صحة التواريخ
      DateTime newStartDate = isStartDate ? picked : _startDate;
      DateTime newEndDate = isStartDate ? _endDate : picked;

      final validationResult = DateValidationService.validateDateRange(
        startDate: newStartDate,
        endDate: newEndDate,
      );

      if (!validationResult.isValid &&
          validationResult.type == DateValidationType.error) {
        if (mounted) {
          DateValidationService.showValidationMessage(
            context: context,
            result: validationResult,
          );
        }
        return;
      }

      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });

      // حفظ التواريخ
      FilterPreferencesService.saveReportDateFilter(
        startDate: _startDate,
        endDate: _endDate,
      );

      // عرض رسالة التحقق
      if (mounted) {
        DateValidationService.showValidationMessage(
          context: context,
          result: validationResult,
        );
      }
    }
  }

  /// إنشاء التقرير المحسن
  void _generateReport() async {
    // التحقق من صحة البيانات
    final validationResult = DateValidationService.validateDateRange(
      startDate: _startDate,
      endDate: _endDate,
    );

    if (!validationResult.isValid &&
        validationResult.type == DateValidationType.error) {
      DateValidationService.showValidationMessage(
        context: context,
        result: validationResult,
      );
      return;
    }

    // التحقق من وجود بيانات
    if (_irrigations.isEmpty && _payments.isEmpty) {
      _showErrorMessage(
          'لا توجد بيانات لإنشاء التقرير. يرجى إضافة بعض العمليات أولاً.');
      return;
    }

    // عرض مؤشر التحميل
    _showLoadingDialog();

    try {
      // إنشاء التقرير من البيانات الحقيقية
      await _generateRealDataReport();

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل

        setState(() {
          _reportGenerated = true;
        });

        _showSuccessMessage(
            'تم إنشاء التقرير بنجاح! تم العثور على ${_reportData.length} عنصر.');

        // عرض إحصائيات سريعة
        _showQuickStats();
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        _showErrorMessage('حدث خطأ في إنشاء التقرير: $e');
      }
    }
  }

  /// إنشاء التقرير من البيانات الحقيقية
  Future<void> _generateRealDataReport() async {
    debugPrint('📊 بدء إنشاء التقرير من البيانات الحقيقية...');

    _reportData.clear();

    // فلترة البيانات حسب التاريخ والفلاتر المحددة
    final filteredIrrigations = _irrigations.where((irrigation) {
      bool matchesDate = _isDateInRange(irrigation.startTime);
      bool matchesClient = _selectedClientId == null ||
          irrigation.clientId.toString() == _selectedClientId;
      bool matchesFarm = _selectedFarmId == null ||
          irrigation.farmId.toString() == _selectedFarmId;
      bool matchesSearch = _searchQuery.isEmpty ||
          _getClientById(irrigation.clientId)
                  ?.name
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ==
              true ||
          _getFarmById(irrigation.farmId)
                  ?.name
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ==
              true;

      return matchesDate && matchesClient && matchesFarm && matchesSearch;
    }).toList();

    final filteredPayments = _payments.where((payment) {
      bool matchesDate = _isDateInRange(payment.createdAt);
      bool matchesClient = _selectedClientId == null ||
          payment.clientId.toString() == _selectedClientId;
      bool matchesSearch = _searchQuery.isEmpty ||
          _getClientById(payment.clientId)
                  ?.name
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ==
              true;

      return matchesDate && matchesClient && matchesSearch;
    }).toList();

    debugPrint(
        '📊 البيانات المفلترة: ${filteredIrrigations.length} تسقية، ${filteredPayments.length} دفعة');

    // إنشاء بيانات التقرير حسب النوع
    switch (_reportType) {
      case 'summary':
        _generateSummaryReportData(filteredIrrigations, filteredPayments);
        break;
      case 'comparison':
        _generateComparisonReportData(filteredIrrigations, filteredPayments);
        break;
      default:
        _generateDetailedReportData(filteredIrrigations, filteredPayments);
    }

    debugPrint('✅ تم إنشاء التقرير بنجاح: ${_reportData.length} عنصر');
  }

  /// إنشاء بيانات التقرير الملخص
  void _generateSummaryReportData(
      List<IrrigationModel> irrigations, List<PaymentModel> payments) {
    _reportData.clear();

    // إحصائيات التسقيات
    double totalIrrigationCost =
        irrigations.fold(0.0, (sum, irrigation) => sum + irrigation.cost);
    double totalDieselConsumption = irrigations.fold(
        0.0, (sum, irrigation) => sum + irrigation.dieselConsumption);
    int totalIrrigationDuration =
        irrigations.fold(0, (sum, irrigation) => sum + irrigation.duration);
    double averageIrrigationCost =
        irrigations.isNotEmpty ? totalIrrigationCost / irrigations.length : 0.0;
    double averageDieselConsumption = irrigations.isNotEmpty
        ? totalDieselConsumption / irrigations.length
        : 0.0;
    double averageDuration = irrigations.isNotEmpty
        ? totalIrrigationDuration / irrigations.length
        : 0.0;

    // إحصائيات المدفوعات
    double totalPayments =
        payments.fold(0.0, (sum, payment) => sum + payment.amount);
    double averagePayment =
        payments.isNotEmpty ? totalPayments / payments.length : 0.0;

    // إضافة إحصائيات التسقيات
    _reportData.add({
      'title': 'إحصائيات التسقيات',
      'total_irrigations': irrigations.length,
      'total_cost': totalIrrigationCost,
      'total_diesel': totalDieselConsumption,
      'total_hours': totalIrrigationDuration,
      'average_cost': averageIrrigationCost,
      'average_diesel': averageDieselConsumption,
      'average_duration': averageDuration,
      'color': Colors.blue,
      'icon': Icons.water_drop,
    });

    // إضافة إحصائيات المدفوعات
    _reportData.add({
      'title': 'إحصائيات المدفوعات',
      'total_payments': payments.length,
      'total_amount': totalPayments,
      'average_amount': averagePayment,
      'color': Colors.green,
      'icon': Icons.payment,
    });

    // إحصائيات العملاء
    final clientStats = <String, Map<String, dynamic>>{};
    for (final irrigation in irrigations) {
      final client = _getClientById(irrigation.clientId);
      if (client != null) {
        final clientName = client.name;
        if (!clientStats.containsKey(clientName)) {
          clientStats[clientName] = {
            'irrigations': 0,
            'cost': 0.0,
            'diesel': 0.0,
            'duration': 0,
            'payments': 0.0,
          };
        }
        clientStats[clientName]!['irrigations'] =
            (clientStats[clientName]!['irrigations'] as int) + 1;
        clientStats[clientName]!['cost'] =
            (clientStats[clientName]!['cost'] as double) + irrigation.cost;
        clientStats[clientName]!['diesel'] =
            (clientStats[clientName]!['diesel'] as double) +
                irrigation.dieselConsumption;
        clientStats[clientName]!['duration'] =
            (clientStats[clientName]!['duration'] as int) + irrigation.duration;
      }
    }

    // إضافة مدفوعات العملاء
    for (final payment in payments) {
      final client = _getClientById(payment.clientId);
      if (client != null) {
        final clientName = client.name;
        if (clientStats.containsKey(clientName)) {
          clientStats[clientName]!['payments'] =
              (clientStats[clientName]!['payments'] as double) + payment.amount;
        }
      }
    }

    // إضافة إحصائيات العملاء
    clientStats.forEach((clientName, stats) {
      _reportData.add({
        'title': 'إحصائيات العميل: $clientName',
        'client_name': clientName,
        'irrigations_count': stats['irrigations'],
        'total_cost': stats['cost'],
        'total_diesel': stats['diesel'],
        'total_duration': stats['duration'],
        'total_payments': stats['payments'],
        'balance': (stats['payments'] as double) - (stats['cost'] as double),
        'color': Colors.orange,
        'icon': Icons.person,
      });
    });

    // إحصائيات المزارع
    final farmStats = <String, Map<String, dynamic>>{};
    for (final irrigation in irrigations) {
      final farm = _getFarmById(irrigation.farmId);
      if (farm != null) {
        final farmName = farm.name;
        if (!farmStats.containsKey(farmName)) {
          farmStats[farmName] = {
            'irrigations': 0,
            'cost': 0.0,
            'diesel': 0.0,
            'duration': 0,
          };
        }
        farmStats[farmName]!['irrigations'] =
            (farmStats[farmName]!['irrigations'] as int) + 1;
        farmStats[farmName]!['cost'] =
            (farmStats[farmName]!['cost'] as double) + irrigation.cost;
        farmStats[farmName]!['diesel'] =
            (farmStats[farmName]!['diesel'] as double) +
                irrigation.dieselConsumption;
        farmStats[farmName]!['duration'] =
            (farmStats[farmName]!['duration'] as int) + irrigation.duration;
      }
    }

    // إضافة إحصائيات المزارع
    farmStats.forEach((farmName, stats) {
      _reportData.add({
        'title': 'إحصائيات المزرعة: $farmName',
        'farm_name': farmName,
        'irrigations_count': stats['irrigations'],
        'total_cost': stats['cost'],
        'total_diesel': stats['diesel'],
        'total_duration': stats['duration'],
        'color': Colors.purple,
        'icon': Icons.agriculture,
      });
    });

    // إحصائيات يومية
    final dailyStats = <String, Map<String, dynamic>>{};
    for (final irrigation in irrigations) {
      final date = DateFormat('dd/MM/yyyy').format(irrigation.startTime);
      if (!dailyStats.containsKey(date)) {
        dailyStats[date] = {
          'irrigations': 0,
          'cost': 0.0,
          'diesel': 0.0,
          'duration': 0,
        };
      }
      dailyStats[date]!['irrigations'] =
          (dailyStats[date]!['irrigations'] as int) + 1;
      dailyStats[date]!['cost'] =
          (dailyStats[date]!['cost'] as double) + irrigation.cost;
      dailyStats[date]!['diesel'] = (dailyStats[date]!['diesel'] as double) +
          irrigation.dieselConsumption;
      dailyStats[date]!['duration'] =
          (dailyStats[date]!['duration'] as int) + irrigation.duration;
    }

    // إضافة أفضل 5 أيام نشاط
    final sortedDays = dailyStats.entries.toList()
      ..sort((a, b) =>
          (b.value['cost'] as double).compareTo(a.value['cost'] as double));

    for (int i = 0; i < sortedDays.length && i < 5; i++) {
      final entry = sortedDays[i];
      _reportData.add({
        'title': 'أفضل يوم نشاط: ${entry.key}',
        'date': entry.key,
        'irrigations_count': entry.value['irrigations'],
        'total_cost': entry.value['cost'],
        'total_diesel': entry.value['diesel'],
        'total_duration': entry.value['duration'],
        'color': Colors.teal,
        'icon': Icons.trending_up,
      });
    }
  }

  /// إنشاء بيانات التقرير المقارن المحسن
  void _generateComparisonReportData(
      List<IrrigationModel> irrigations, List<PaymentModel> payments) {
    _reportData.clear();

    // مقارنة بين النصف الأول والثاني من الفترة
    final midDate = DateTime(_startDate.year, _startDate.month,
        _startDate.day + ((_endDate.difference(_startDate).inDays) ~/ 2));

    final firstHalfIrrigations =
        irrigations.where((i) => i.startTime.isBefore(midDate)).toList();
    final secondHalfIrrigations = irrigations
        .where((i) =>
            i.startTime.isAfter(midDate) ||
            i.startTime.isAtSameMomentAs(midDate))
        .toList();

    final firstHalfPayments =
        payments.where((p) => p.createdAt.isBefore(midDate)).toList();
    final secondHalfPayments = payments
        .where((p) =>
            p.createdAt.isAfter(midDate) ||
            p.createdAt.isAtSameMomentAs(midDate))
        .toList();

    // إحصائيات النصف الأول
    final firstHalfCost =
        firstHalfIrrigations.fold(0.0, (sum, i) => sum + i.cost);
    final firstHalfDiesel =
        firstHalfIrrigations.fold(0.0, (sum, i) => sum + i.dieselConsumption);
    final firstHalfPaymentsAmount =
        firstHalfPayments.fold(0.0, (sum, p) => sum + p.amount);

    _reportData.add({
      'title': 'النصف الأول من الفترة',
      'period': 'النصف الأول',
      'current_count': firstHalfIrrigations.length,
      'current_cost': firstHalfCost,
      'current_diesel': firstHalfDiesel,
      'current_payments': firstHalfPaymentsAmount,
      'previous_count': 0,
      'previous_cost': 0.0,
      'previous_diesel': 0.0,
      'previous_payments': 0.0,
      'count_change': firstHalfIrrigations.length,
      'cost_change': firstHalfCost,
      'diesel_change': firstHalfDiesel,
      'payments_change': firstHalfPaymentsAmount,
      'count_change_percent': 100.0,
      'cost_change_percent': 100.0,
      'diesel_change_percent': 100.0,
      'payments_change_percent': 100.0,
      'color': Colors.blue,
      'icon': Icons.trending_up,
      'trend': 'بداية الفترة',
    });

    // إحصائيات النصف الثاني
    final secondHalfCost =
        secondHalfIrrigations.fold(0.0, (sum, i) => sum + i.cost);
    final secondHalfDiesel =
        secondHalfIrrigations.fold(0.0, (sum, i) => sum + i.dieselConsumption);
    final secondHalfPaymentsAmount =
        secondHalfPayments.fold(0.0, (sum, p) => sum + p.amount);

    final countChange =
        secondHalfIrrigations.length - firstHalfIrrigations.length;
    final costChange = secondHalfCost - firstHalfCost;
    final dieselChange = secondHalfDiesel - firstHalfDiesel;
    final paymentsChange = secondHalfPaymentsAmount - firstHalfPaymentsAmount;

    final countChangePercent = firstHalfIrrigations.isNotEmpty
        ? (countChange / firstHalfIrrigations.length) * 100
        : 0.0;
    final costChangePercent =
        firstHalfCost > 0 ? (costChange / firstHalfCost) * 100 : 0.0;
    final dieselChangePercent =
        firstHalfDiesel > 0 ? (dieselChange / firstHalfDiesel) * 100 : 0.0;
    final paymentsChangePercent = firstHalfPaymentsAmount > 0
        ? (paymentsChange / firstHalfPaymentsAmount) * 100
        : 0.0;

    _reportData.add({
      'title': 'النصف الثاني من الفترة',
      'period': 'النصف الثاني',
      'current_count': secondHalfIrrigations.length,
      'current_cost': secondHalfCost,
      'current_diesel': secondHalfDiesel,
      'current_payments': secondHalfPaymentsAmount,
      'previous_count': firstHalfIrrigations.length,
      'previous_cost': firstHalfCost,
      'previous_diesel': firstHalfDiesel,
      'previous_payments': firstHalfPaymentsAmount,
      'count_change': countChange,
      'cost_change': costChange,
      'diesel_change': dieselChange,
      'payments_change': paymentsChange,
      'count_change_percent': countChangePercent,
      'cost_change_percent': costChangePercent,
      'diesel_change_percent': dieselChangePercent,
      'payments_change_percent': paymentsChangePercent,
      'color': countChange >= 0 ? Colors.green : Colors.red,
      'icon': countChange >= 0 ? Icons.trending_up : Icons.trending_down,
      'trend': countChange >= 0 ? 'تحسن' : 'انخفاض',
    });

    // مقارنة العملاء
    final clientComparison = <String, Map<String, dynamic>>{};

    // إحصائيات النصف الأول للعملاء
    for (final irrigation in firstHalfIrrigations) {
      final client = _getClientById(irrigation.clientId);
      if (client != null) {
        final clientName = client.name;
        if (!clientComparison.containsKey(clientName)) {
          clientComparison[clientName] = {
            'first_half': {'count': 0, 'cost': 0.0, 'diesel': 0.0},
            'second_half': {'count': 0, 'cost': 0.0, 'diesel': 0.0},
          };
        }
        clientComparison[clientName]!['first_half']!['count'] =
            (clientComparison[clientName]!['first_half']!['count'] as int) + 1;
        clientComparison[clientName]!['first_half']!['cost'] =
            (clientComparison[clientName]!['first_half']!['cost'] as double) +
                irrigation.cost;
        clientComparison[clientName]!['first_half']!['diesel'] =
            (clientComparison[clientName]!['first_half']!['diesel'] as double) +
                irrigation.dieselConsumption;
      }
    }

    // إحصائيات النصف الثاني للعملاء
    for (final irrigation in secondHalfIrrigations) {
      final client = _getClientById(irrigation.clientId);
      if (client != null) {
        final clientName = client.name;
        if (!clientComparison.containsKey(clientName)) {
          clientComparison[clientName] = {
            'first_half': {'count': 0, 'cost': 0.0, 'diesel': 0.0},
            'second_half': {'count': 0, 'cost': 0.0, 'diesel': 0.0},
          };
        }
        clientComparison[clientName]!['second_half']!['count'] =
            (clientComparison[clientName]!['second_half']!['count'] as int) + 1;
        clientComparison[clientName]!['second_half']!['cost'] =
            (clientComparison[clientName]!['second_half']!['cost'] as double) +
                irrigation.cost;
        clientComparison[clientName]!['second_half']!['diesel'] =
            (clientComparison[clientName]!['second_half']!['diesel']
                    as double) +
                irrigation.dieselConsumption;
      }
    }

    // إضافة مقارنة العملاء
    clientComparison.forEach((clientName, stats) {
      final firstHalf = stats['first_half'] as Map<String, dynamic>;
      final secondHalf = stats['second_half'] as Map<String, dynamic>;

      final countChange =
          (secondHalf['count'] as int) - (firstHalf['count'] as int);
      final costChange =
          (secondHalf['cost'] as double) - (firstHalf['cost'] as double);
      final dieselChange =
          (secondHalf['diesel'] as double) - (firstHalf['diesel'] as double);

      final countChangePercent = firstHalf['count'] > 0
          ? (countChange / firstHalf['count']) * 100
          : 0.0;
      final costChangePercent =
          firstHalf['cost'] > 0 ? (costChange / firstHalf['cost']) * 100 : 0.0;
      final dieselChangePercent = firstHalf['diesel'] > 0
          ? (dieselChange / firstHalf['diesel']) * 100
          : 0.0;

      _reportData.add({
        'title': 'مقارنة العميل: $clientName',
        'client_name': clientName,
        'current_count': secondHalf['count'],
        'current_cost': secondHalf['cost'],
        'current_diesel': secondHalf['diesel'],
        'previous_count': firstHalf['count'],
        'previous_cost': firstHalf['cost'],
        'previous_diesel': firstHalf['diesel'],
        'count_change': countChange,
        'cost_change': costChange,
        'diesel_change': dieselChange,
        'count_change_percent': countChangePercent,
        'cost_change_percent': costChangePercent,
        'diesel_change_percent': dieselChangePercent,
        'color': countChange >= 0 ? Colors.green : Colors.red,
        'icon': countChange >= 0 ? Icons.trending_up : Icons.trending_down,
        'trend': countChange >= 0 ? 'تحسن' : 'انخفاض',
      });
    });
  }

  /// إنشاء بيانات التقرير المفصل المحسن
  void _generateDetailedReportData(
      List<IrrigationModel> irrigations, List<PaymentModel> payments) {
    _reportData.clear();

    // إضافة جميع التسقيات مع تفاصيل إضافية
    for (final irrigation in irrigations) {
      final client = _getClientById(irrigation.clientId);
      final farm = _getFarmById(irrigation.farmId);

      _reportData.add({
        'type': 'irrigation',
        'title': 'تسقية - ${client?.name ?? 'غير محدد'}',
        'client_name': client?.name ?? 'غير محدد',
        'farm_name': farm?.name ?? 'غير محدد',
        'amount': irrigation.cost,
        'duration': irrigation.duration,
        'diesel_consumption': irrigation.dieselConsumption,
        'date': DateFormat('dd/MM/yyyy').format(irrigation.startTime),
        'time': DateFormat('HH:mm').format(irrigation.startTime),
        'notes': (irrigation.notes?.isNotEmpty ?? false)
            ? irrigation.notes!
            : 'لا توجد ملاحظات',
        'color': Colors.blue,
        'icon': Icons.water_drop,
        'details': {
          'المزرعة': farm?.name ?? 'غير محدد',
          'المدة': '${irrigation.duration} دقيقة',
          'الديزل': '${irrigation.dieselConsumption} لتر',
          'التكلفة': '${irrigation.cost} ريال',
          'التاريخ': DateFormat('dd/MM/yyyy').format(irrigation.startTime),
          'الوقت': DateFormat('HH:mm').format(irrigation.startTime),
        },
      });
    }

    // إضافة جميع المدفوعات مع تفاصيل إضافية
    for (final payment in payments) {
      final client = _getClientById(payment.clientId);

      _reportData.add({
        'type': 'payment',
        'title': 'دفعة - ${client?.name ?? 'غير محدد'}',
        'client_name': client?.name ?? 'غير محدد',
        'amount': payment.amount,
        'date': DateFormat('dd/MM/yyyy').format(payment.createdAt),
        'time': DateFormat('HH:mm').format(payment.createdAt),
        'notes': (payment.notes?.isNotEmpty ?? false)
            ? payment.notes!
            : 'لا توجد ملاحظات',
        'color': Colors.green,
        'icon': Icons.payment,
        'details': {
          'المبلغ': '${payment.amount} ريال',
          'التاريخ': DateFormat('dd/MM/yyyy').format(payment.createdAt),
          'الوقت': DateFormat('HH:mm').format(payment.createdAt),
          'الطريقة': 'غير محدد',
        },
      });
    }

    // ترتيب البيانات حسب التاريخ والوقت
    _reportData.sort((a, b) {
      final dateA = DateFormat('dd/MM/yyyy').parse(a['date'] as String);
      final dateB = DateFormat('dd/MM/yyyy').parse(b['date'] as String);
      if (dateA.isAtSameMomentAs(dateB)) {
        final timeA = DateFormat('HH:mm').parse(a['time'] as String);
        final timeB = DateFormat('HH:mm').parse(b['time'] as String);
        return timeB.compareTo(timeA); // الأحدث أولاً
      }
      return dateB.compareTo(dateA); // الأحدث أولاً
    });
  }

  /// التحقق من مرور الفلاتر
  bool _passesFilters({IrrigationModel? irrigation, PaymentModel? payment}) {
    // فلتر العميل
    if (_selectedClientId != null) {
      if (irrigation != null &&
          irrigation.clientId.toString() != _selectedClientId) {
        return false;
      }
      if (payment != null && payment.clientId.toString() != _selectedClientId) {
        return false;
      }
    }

    // فلتر المزرعة
    if (_selectedFarmId != null && irrigation != null) {
      if (irrigation.farmId.toString() != _selectedFarmId) {
        return false;
      }
    }

    return true;
  }

  /// الحصول على البيانات المفلترة للتقرير
  List<Map<String, dynamic>> _getFilteredReportData() {
    if (_searchQuery.isEmpty) {
      return _reportData;
    }

    return _reportData.where((item) {
      final searchLower = _searchQuery.toLowerCase();
      final clientName = (item['client_name'] ?? '').toString().toLowerCase();
      final farmName = (item['farm_name'] ?? '').toString().toLowerCase();
      final date = (item['date'] ?? '').toString().toLowerCase();

      return clientName.contains(searchLower) ||
          farmName.contains(searchLower) ||
          date.contains(searchLower);
    }).toList();
  }

  /// عرض تفاصيل العنصر
  void _showItemDetails(Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
            'تفاصيل ${item['type'] == 'irrigation' ? 'التسقية' : 'الدفعة'}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('التاريخ', item['date'] ?? ''),
            _buildDetailRow('العميل', item['client_name'] ?? ''),
            if (item['farm_name']?.isNotEmpty == true)
              _buildDetailRow('المزرعة', item['farm_name']),
            _buildDetailRow(
                'المبلغ', '${item['amount']?.toStringAsFixed(2) ?? '0'} ريال'),
            if (item['duration'] != null)
              _buildDetailRow(
                  'المدة', '${item['duration']?.toStringAsFixed(1)} ساعة'),
            if (item['diesel_consumption'] != null)
              _buildDetailRow('استهلاك الديزل',
                  '${item['diesel_consumption']?.toStringAsFixed(2)} لتر'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة التقرير
  IconData _getReportIcon() {
    switch (_reportType) {
      case 'summary':
        return Icons.summarize;
      case 'comparison':
        return Icons.compare_arrows;
      default:
        return Icons.list_alt;
    }
  }

  /// الحصول على عنوان التقرير
  String _getEnhancedReportTitle() {
    switch (_reportType) {
      case 'summary':
        return 'تقرير ملخص - ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}';
      case 'comparison':
        return 'تقرير مقارن - ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}';
      case 'detailed':
        return 'تقرير مفصل - ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}';
      default:
        return 'تقرير مخصص';
    }
  }

  /// عرض حوار التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: AppTheme.primaryColor),
            const SizedBox(height: 16),
            Text('جاري إنشاء ${_getEnhancedReportTitle()}...'),
            const SizedBox(height: 8),
            Text(
              'يتم تحليل البيانات وإعداد التقرير',
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
      ),
    );
  }

  /// تصدير التقرير
  Future<void> _exportReport() async {
    if (_reportData.isEmpty) {
      _showErrorMessage('لا توجد بيانات لتصديرها. يرجى إنشاء التقرير أولاً.');
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      // إعداد بيانات التصدير
      final headers = _getReportHeaders();
      final title = _getEnhancedReportTitle();
      final subtitle = _getReportSubtitle();
      final summary = _getReportSummary();

      // عرض حوار اختيار نوع التصدير
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.file_download, color: Colors.blue),
              SizedBox(width: 8),
              Text('تصدير التقرير'),
            ],
          ),
          content: const Text('اختر نوع التصدير المطلوب:'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ReportExportService.exportToPDF(
                  title: title,
                  data: _reportData,
                  headers: headers,
                  subtitle: subtitle,
                  summary: summary,
                );
              },
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.picture_as_pdf, color: Colors.red),
                  SizedBox(width: 4),
                  Text('PDF'),
                ],
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                ReportExportService.exportToExcel(
                  title: title,
                  data: _reportData,
                  headers: headers,
                  subtitle: subtitle,
                  summary: summary,
                );
              },
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.table_chart, color: Colors.green),
                  SizedBox(width: 4),
                  Text('Excel'),
                ],
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('❌ خطأ في تصدير التقرير: $e');
      if (mounted) {
        _showErrorMessage('حدث خطأ أثناء تصدير التقرير: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// الحصول على رؤوس التقرير
  List<String> _getReportHeaders() {
    if (_reportData.isEmpty) return [];

    // تحديد الرؤوس حسب نوع التقرير
    switch (_reportType) {
      case 'detailed':
        return [
          'النوع',
          'التاريخ',
          'العميل',
          'المزرعة',
          'المبلغ/التكلفة',
          'المدة',
          'الديزل',
          'الملاحظات'
        ];
      case 'comparison':
        return [
          'العنصر',
          'القيمة الحالية',
          'القيمة السابقة',
          'التغيير',
          'النسبة المئوية',
          'الاتجاه'
        ];
      case 'summary':
        return ['الفئة', 'العدد', 'الإجمالي', 'المتوسط', 'النسبة المئوية'];
      default:
        return _reportData.first.keys.toList();
    }
  }

  /// الحصول على عنوان فرعي للتقرير
  String? _getReportSubtitle() {
    String subtitle = 'نوع التقرير: ${_getReportTypeDisplayName()}';

    if (_selectedClientId != null) {
      final client = _getClientById(int.parse(_selectedClientId!));
      subtitle += ' | العميل: ${client?.name ?? 'غير محدد'}';
    }

    if (_selectedFarmId != null) {
      final farm = _getFarmById(int.parse(_selectedFarmId!));
      subtitle += ' | المزرعة: ${farm?.name ?? 'غير محدد'}';
    }

    return subtitle;
  }

  /// الحصول على ملخص التقرير
  Map<String, dynamic>? _getReportSummary() {
    if (_reportData.isEmpty) return null;

    final irrigations =
        _reportData.where((item) => item['type'] == 'irrigation').toList();
    final payments =
        _reportData.where((item) => item['type'] == 'payment').toList();

    double totalIrrigationCost =
        irrigations.fold(0.0, (sum, item) => sum + (item['amount'] ?? 0));
    double totalPaymentAmount =
        payments.fold(0.0, (sum, item) => sum + (item['amount'] ?? 0));
    double totalDiesel = irrigations.fold(
        0.0, (sum, item) => sum + (item['diesel_consumption'] ?? 0));
    double totalHours =
        irrigations.fold(0.0, (sum, item) => sum + (item['duration'] ?? 0));

    return {
      'عدد التسقيات': irrigations.length,
      'عدد المدفوعات': payments.length,
      'إجمالي تكلفة التسقيات': '${totalIrrigationCost.toStringAsFixed(2)} ريال',
      'إجمالي المدفوعات': '${totalPaymentAmount.toStringAsFixed(2)} ريال',
      'إجمالي استهلاك الديزل': '${totalDiesel.toStringAsFixed(2)} لتر',
      'إجمالي ساعات العمل': '${totalHours.toStringAsFixed(2)} ساعة',
      'الرصيد المتبقي':
          '${(totalPaymentAmount - totalIrrigationCost).toStringAsFixed(2)} ريال',
    };
  }

  /// تصدير إلى PDF (HTML)
  Future<void> _exportToPDF() async {
    try {
      _showLoadingMessage('جاري تصدير التقرير...');

      // إنشاء محتوى HTML محسن مع دعم العربية
      final htmlContent = _generateEnhancedHTMLReport();

      // حفظ الملف
      final fileName =
          'تقرير_${_getReportTypeDisplayName()}_${DateFormat('yyyy-MM-dd').format(DateTime.now())}.html';

      // محاولة حفظ الملف باستخدام file_picker
      try {
        final result = await FilePicker.platform.saveFile(
          dialogTitle: 'حفظ التقرير كـ PDF',
          fileName: fileName,
          type: FileType.custom,
          allowedExtensions: ['html'],
        );

        if (result != null) {
          final file = File(result);
          await file.writeAsString(htmlContent, encoding: utf8);

          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            _showSuccessMessage(
                'تم تصدير التقرير بنجاح!\nالمسار: $result\n\nيمكنك طباعة الملف كـ PDF من المتصفح');

            // عرض خيار المشاركة
            _showShareOptions(result);
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            _showErrorMessage('تم إلغاء عملية التصدير');
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          _showErrorMessage('فشل في حفظ الملف: $e');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showErrorMessage('فشل في تصدير التقرير: $e');
      }
    }
  }

  /// إنشاء تقرير HTML محسن مع دعم العربية الكامل
  String _generateEnhancedHTMLReport() {
    final data = _getFilteredReportData();
    final buffer = StringBuffer();

    buffer.writeln('<!DOCTYPE html>');
    buffer.writeln('<html dir="rtl" lang="ar">');
    buffer.writeln('<head>');
    buffer.writeln('<meta charset="UTF-8">');
    buffer.writeln(
        '<meta name="viewport" content="width=device-width, initial-scale=1.0">');
    buffer.writeln('<title>${_getEnhancedReportTitle()}</title>');
    buffer.writeln('<style>');
    buffer.writeln(
        '@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap");');
    buffer.writeln('* { box-sizing: border-box; }');
    buffer.writeln(
        'body { font-family: "Noto Sans Arabic", Arial, sans-serif; margin: 0; padding: 20px; direction: rtl; background-color: #f5f5f5; }');
    buffer.writeln(
        '.container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }');
    buffer.writeln(
        'h1 { color: #2196F3; text-align: center; margin-bottom: 30px; font-size: 28px; font-weight: 700; }');
    buffer.writeln(
        '.header { background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 20px; border-radius: 8px; margin-bottom: 30px; border-right: 5px solid #2196F3; }');
    buffer.writeln(
        '.header h2 { margin: 0 0 10px 0; color: #1976d2; font-size: 20px; }');
    buffer.writeln(
        '.header p { margin: 5px 0; color: #424242; font-size: 16px; }');
    buffer.writeln(
        '.stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }');
    buffer.writeln(
        '.stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-top: 3px solid #2196F3; }');
    buffer.writeln(
        '.stat-number { font-size: 24px; font-weight: 700; color: #2196F3; margin-bottom: 5px; }');
    buffer.writeln('.stat-label { font-size: 14px; color: #666; }');
    buffer.writeln(
        'table { width: 100%; border-collapse: collapse; margin-top: 20px; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }');
    buffer.writeln(
        'th { background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%); color: white; padding: 15px 12px; text-align: right; font-weight: 700; font-size: 14px; }');
    buffer.writeln(
        'td { border-bottom: 1px solid #e0e0e0; padding: 12px; text-align: right; font-size: 14px; }');
    buffer.writeln('tr:nth-child(even) { background-color: #f9f9f9; }');
    buffer.writeln('tr:hover { background-color: #e3f2fd; }');
    buffer.writeln('.amount { font-weight: 600; color: #4caf50; }');
    buffer.writeln('.cost { font-weight: 600; color: #ff9800; }');
    buffer.writeln(
        '.footer { margin-top: 30px; text-align: center; color: #666; font-size: 12px; border-top: 1px solid #e0e0e0; padding-top: 20px; }');
    buffer.writeln(
        '@media print { body { background: white; } .container { box-shadow: none; } }');
    buffer.writeln('</style>');
    buffer.writeln('</head>');
    buffer.writeln('<body>');
    buffer.writeln('<div class="container">');

    // العنوان الرئيسي
    buffer.writeln('<h1>${_getEnhancedReportTitle()}</h1>');

    // معلومات التقرير
    buffer.writeln('<div class="header">');
    buffer.writeln('<h2>معلومات التقرير</h2>');
    buffer.writeln(
        '<p><strong>نوع التقرير:</strong> ${_getReportTypeDisplayName()}</p>');
    buffer.writeln(
        '<p><strong>الفترة الزمنية:</strong> من ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}</p>');
    buffer.writeln('<p><strong>عدد العناصر:</strong> ${data.length} عنصر</p>');
    buffer.writeln(
        '<p><strong>تاريخ الإنشاء:</strong> ${DateFormat('dd/MM/yyyy - HH:mm').format(DateTime.now())}</p>');
    if (_selectedClientId != null) {
      final client = _getClientById(int.parse(_selectedClientId!));
      buffer.writeln(
          '<p><strong>العميل المحدد:</strong> ${client?.name ?? 'غير محدد'}</p>');
    }
    if (_selectedFarmId != null) {
      final farm = _getFarmById(int.parse(_selectedFarmId!));
      buffer.writeln(
          '<p><strong>المزرعة المحددة:</strong> ${farm?.name ?? 'غير محدد'}</p>');
    }
    buffer.writeln('</div>');

    // إحصائيات سريعة
    _addQuickStatsToHTML(buffer, data);

    // الجدول
    if (data.isNotEmpty) {
      buffer.writeln('<table>');
      buffer.writeln('<thead>');
      buffer.writeln('<tr>');

      // رؤوس الأعمدة
      final firstItem = data.first;
      firstItem.keys.forEach((key) {
        buffer.writeln('<th>${_getColumnDisplayName(key)}</th>');
      });

      buffer.writeln('</tr>');
      buffer.writeln('</thead>');
      buffer.writeln('<tbody>');

      // البيانات
      for (final item in data) {
        buffer.writeln('<tr>');
        item.values.forEach((value) {
          buffer.writeln('<td>${value?.toString() ?? ''}</td>');
        });
        buffer.writeln('</tr>');
      }

      buffer.writeln('</tbody>');
      buffer.writeln('</table>');
    } else {
      buffer.writeln(
          '<div style="text-align: center; padding: 50px; color: #666; font-size: 18px;">');
      buffer.writeln('<p>لا توجد بيانات لعرضها في الفترة المحددة</p>');
      buffer.writeln('</div>');
    }

    // تذييل التقرير
    buffer.writeln('<div class="footer">');
    buffer.writeln('<p>تم إنشاء هذا التقرير بواسطة نظام إدارة التسقيات</p>');
    buffer.writeln('<p>© ${DateTime.now().year} - جميع الحقوق محفوظة</p>');
    buffer.writeln('</div>');

    buffer.writeln('</div>'); // إغلاق container
    buffer.writeln('</body>');
    buffer.writeln('</html>');

    return buffer.toString();
  }

  /// إضافة الإحصائيات السريعة إلى HTML
  void _addQuickStatsToHTML(
      StringBuffer buffer, List<Map<String, dynamic>> data) {
    if (data.isEmpty) return;

    // حساب الإحصائيات
    double totalAmount = 0;
    double totalCost = 0;
    double totalDiesel = 0;
    int totalDuration = 0;
    int irrigationCount = 0;
    int paymentCount = 0;

    for (final item in data) {
      if (item['amount'] != null)
        totalAmount += (item['amount'] as num).toDouble();
      if (item['cost'] != null) totalCost += (item['cost'] as num).toDouble();
      if (item['diesel_consumption'] != null)
        totalDiesel += (item['diesel_consumption'] as num).toDouble();
      if (item['duration'] != null)
        totalDuration += (item['duration'] as num).toInt();
      if (item['type'] == 'تسقية') irrigationCount++;
      if (item['type'] == 'دفعة') paymentCount++;
    }

    buffer.writeln('<div class="stats-grid">');

    if (irrigationCount > 0) {
      buffer.writeln('<div class="stat-card">');
      buffer.writeln('<div class="stat-number">$irrigationCount</div>');
      buffer.writeln('<div class="stat-label">عدد التسقيات</div>');
      buffer.writeln('</div>');
    }

    if (paymentCount > 0) {
      buffer.writeln('<div class="stat-card">');
      buffer.writeln('<div class="stat-number">$paymentCount</div>');
      buffer.writeln('<div class="stat-label">عدد المدفوعات</div>');
      buffer.writeln('</div>');
    }

    if (totalCost > 0) {
      buffer.writeln('<div class="stat-card">');
      buffer.writeln(
          '<div class="stat-number">${totalCost.toStringAsFixed(2)} ر.س</div>');
      buffer.writeln('<div class="stat-label">إجمالي التكلفة</div>');
      buffer.writeln('</div>');
    }

    if (totalAmount > 0) {
      buffer.writeln('<div class="stat-card">');
      buffer.writeln(
          '<div class="stat-number">${totalAmount.toStringAsFixed(2)} ر.س</div>');
      buffer.writeln('<div class="stat-label">إجمالي المدفوعات</div>');
      buffer.writeln('</div>');
    }

    if (totalDiesel > 0) {
      buffer.writeln('<div class="stat-card">');
      buffer.writeln(
          '<div class="stat-number">${totalDiesel.toStringAsFixed(1)} لتر</div>');
      buffer.writeln('<div class="stat-label">استهلاك الديزل</div>');
      buffer.writeln('</div>');
    }

    if (totalDuration > 0) {
      buffer.writeln('<div class="stat-card">');
      buffer.writeln('<div class="stat-number">$totalDuration دقيقة</div>');
      buffer.writeln('<div class="stat-label">مدة التسقية</div>');
      buffer.writeln('</div>');
    }

    buffer.writeln('</div>');
  }

  /// عرض خيارات المشاركة
  void _showShareOptions(String filePath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.share, color: Colors.blue),
            SizedBox(width: 8),
            Text('مشاركة التقرير'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('تم حفظ التقرير بنجاح! يمكنك الآن:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.open_in_browser, color: Colors.blue),
              title: const Text('فتح في المتصفح'),
              subtitle: const Text('لطباعة التقرير كـ PDF'),
              onTap: () {
                Navigator.pop(context);
                _openFileInBrowser(filePath);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share, color: Colors.green),
              title: const Text('مشاركة الملف'),
              subtitle: const Text('إرسال التقرير عبر التطبيقات'),
              onTap: () {
                Navigator.pop(context);
                _shareFile(filePath);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// فتح الملف في المتصفح
  void _openFileInBrowser(String filePath) {
    try {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.info, color: Colors.blue),
              SizedBox(width: 8),
              Text('فتح الملف'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('تم حفظ الملف في:'),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SelectableText(
                  filePath,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
              const SizedBox(height: 16),
              const Text('يمكنك:'),
              const Text('• فتح الملف من مدير الملفات'),
              const Text('• نسخ المسار أعلاه'),
              const Text('• فتح الملف في المتصفح للطباعة كـ PDF'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showErrorMessage('فشل في عرض معلومات الملف: $e');
    }
  }

  /// مشاركة الملف
  void _shareFile(String filePath) {
    try {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.share, color: Colors.green),
              SizedBox(width: 8),
              Text('مشاركة الملف'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('يمكنك مشاركة الملف عبر:'),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.email, color: Colors.blue),
                title: const Text('البريد الإلكتروني'),
                subtitle: const Text('إرفاق الملف في رسالة'),
                onTap: () {
                  Navigator.pop(context);
                  _showSuccessMessage('يمكنك إرفاق الملف من:\n$filePath');
                },
              ),
              ListTile(
                leading: const Icon(Icons.message, color: Colors.green),
                title: const Text('تطبيقات المراسلة'),
                subtitle: const Text('واتساب، تيليجرام، إلخ'),
                onTap: () {
                  Navigator.pop(context);
                  _showSuccessMessage('يمكنك مشاركة الملف من:\n$filePath');
                },
              ),
              ListTile(
                leading: const Icon(Icons.cloud_upload, color: Colors.orange),
                title: const Text('التخزين السحابي'),
                subtitle: const Text('Google Drive، Dropbox، إلخ'),
                onTap: () {
                  Navigator.pop(context);
                  _showSuccessMessage('يمكنك رفع الملف من:\n$filePath');
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showErrorMessage('فشل في عرض خيارات المشاركة: $e');
    }
  }

  /// الحصول على اسم العمود للعرض
  String _getColumnDisplayName(String key) {
    switch (key) {
      case 'client_name':
        return 'اسم العميل';
      case 'farm_name':
        return 'اسم المزرعة';
      case 'amount':
        return 'المبلغ';
      case 'cost':
        return 'التكلفة';
      case 'date':
        return 'التاريخ';
      case 'type':
        return 'النوع';
      case 'duration':
        return 'المدة';
      case 'diesel_consumption':
        return 'استهلاك الديزل';
      case 'notes':
        return 'ملاحظات';
      default:
        return key;
    }
  }

  /// تصدير إلى Excel (CSV)
  Future<void> _exportToExcel() async {
    try {
      _showLoadingMessage('جاري تصدير التقرير...');

      // إنشاء محتوى CSV محسن مع دعم العربية
      final csvContent = _generateEnhancedCSVContent();

      // حفظ الملف
      final fileName =
          'تقرير_${_getReportTypeDisplayName()}_${DateFormat('yyyy-MM-dd').format(DateTime.now())}.csv';

      try {
        final result = await FilePicker.platform.saveFile(
          dialogTitle: 'حفظ التقرير',
          fileName: fileName,
          type: FileType.custom,
          allowedExtensions: ['csv'],
        );

        if (result != null) {
          final file = File(result);
          await file.writeAsString(csvContent, encoding: utf8);

          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            _showSuccessMessage('تم تصدير التقرير بنجاح!\nالمسار: $result');
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            _showErrorMessage('تم إلغاء عملية التصدير');
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          _showErrorMessage('فشل في حفظ الملف: $e');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showErrorMessage('فشل في تصدير التقرير: $e');
      }
    }
  }

  /// إنشاء محتوى CSV محسن مع دعم العربية الكامل
  String _generateEnhancedCSVContent() {
    final data = _getFilteredReportData();
    final buffer = StringBuffer();

    // إضافة BOM للدعم العربي في Excel
    buffer.write('\uFEFF');

    // إضافة معلومات التقرير في الأعلى
    buffer.writeln('تقرير,${_getEnhancedReportTitle()}');
    buffer.writeln('نوع التقرير,${_getReportTypeDisplayName()}');
    buffer.writeln(
        'الفترة الزمنية,من ${DateFormat('dd/MM/yyyy').format(_startDate)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate)}');
    buffer.writeln('عدد العناصر,${data.length}');
    buffer.writeln(
        'تاريخ الإنشاء,${DateFormat('dd/MM/yyyy - HH:mm').format(DateTime.now())}');
    buffer.writeln(''); // سطر فارغ

    if (data.isNotEmpty) {
      // رؤوس الأعمدة
      final headers =
          data.first.keys.map((key) => _getColumnDisplayName(key)).toList();
      buffer.writeln(headers.join(','));

      // البيانات
      for (final item in data) {
        final row = item.values.map((value) {
          final stringValue = value?.toString() ?? '';
          // إضافة علامات اقتباس إذا كان النص يحتوي على فاصلة أو علامة اقتباس
          if (stringValue.contains(',') ||
              stringValue.contains('"') ||
              stringValue.contains('\n')) {
            return '"${stringValue.replaceAll('"', '""')}"';
          }
          return stringValue;
        }).toList();
        buffer.writeln(row.join(','));
      }
    }

    return buffer.toString();
  }

  /// طباعة التقرير
  void _printReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.print, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('طباعة التقرير'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('إعدادات الطباعة:',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            Text('نوع التقرير: ${_getEnhancedReportTitle()}'),
            Text('عدد العناصر: ${_reportData.length}'),
            Text(
                'الفترة: ${DateFormat('dd/MM/yyyy').format(_startDate)} - ${DateFormat('dd/MM/yyyy').format(_endDate)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _showSuccessMessage('تم إرسال التقرير إلى الطابعة بنجاح!');
            },
            icon: const Icon(Icons.print, color: Colors.white),
            label: const Text('طباعة', style: TextStyle(color: Colors.white)),
            style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// عرض رسالة تحميل
  void _showLoadingMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: AppTheme.primaryColor,
          duration: const Duration(seconds: 10),
        ),
      );
    }
  }

  /// الحصول على اسم نوع التقرير للعرض
  String _getReportTypeDisplayName() {
    switch (_reportType) {
      case 'summary':
        return 'التقرير الملخص';
      case 'comparison':
        return 'التقرير المقارن';
      default:
        return 'التقرير المفصل';
    }
  }

  /// عرض التقرير بملء الشاشة
  Future<void> _showFullscreenReport() async {
    if (!_reportGenerated || _reportData.isEmpty) {
      _showErrorMessage('يجب إنشاء التقرير أولاً');
      return;
    }

    try {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FullscreenReportPage(
            reportTitle: _getEnhancedReportTitle(),
            reportType: _getReportTypeDisplayName(),
            startDate: _startDate,
            endDate: _endDate,
            reportData: _getFilteredReportData(),
            onExportPDF: () async {
              await _exportToPDF();
            },
            onExportExcel: () async {
              await _exportToExcel();
            },
            onPrint: () {
              _printReport();
            },
          ),
        ),
      );
    } catch (e) {
      _showErrorMessage('فشل في عرض التقرير بملء الشاشة: $e');
    }
  }

  /// عرض إحصائيات سريعة
  void _showQuickStats() {
    if (_reportData.isEmpty) return;

    String statsMessage = '';

    switch (_reportType) {
      case 'summary':
        final irrigationItem = _reportData.firstWhere(
          (item) => item['title'] == 'إحصائيات التسقيات',
          orElse: () => {},
        );
        if (irrigationItem.isNotEmpty) {
          statsMessage =
              'إجمالي التسقيات: ${irrigationItem['total_irrigations']}\n';
          statsMessage +=
              'إجمالي التكلفة: ${irrigationItem['total_cost']?.toStringAsFixed(2)} ريال';
        }
        break;
      case 'comparison':
        statsMessage = 'تم إنشاء تقرير مقارن بين فترتين';
        break;
      default:
        statsMessage = 'تم إنشاء تقرير مفصل بـ ${_reportData.length} عنصر';
    }

    if (statsMessage.isNotEmpty && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(statsMessage),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// تهيئة البيانات الحقيقية
  Future<void> _initializeRealisticData() async {
    if (_dataInitialized) return;

    try {
      debugPrint('🚀 بدء تهيئة البيانات الحقيقية...');

      // إنشاء مجموعة بيانات حقيقية كاملة
      final realisticData = RealDataManager.generateCompleteRealisticDataset();

      // تطبيق إصلاح تلقائي للبيانات
      final repairedData = await DataRepairService.performComprehensiveRepair(
        clients: realisticData['clients'],
        farms: realisticData['farms'],
        irrigations: realisticData['irrigations'],
        payments: realisticData['payments'],
      );

      setState(() {
        if (repairedData['success'] == true) {
          final finalData = repairedData['repaired_data'];
          _clients = finalData['clients'];
          _farms = finalData['farms'];
          _irrigations = finalData['irrigations'];
          _payments = finalData['payments'];
        } else {
          _clients = realisticData['clients'];
          _farms = realisticData['farms'];
          _irrigations = realisticData['irrigations'];
          _payments = realisticData['payments'];
        }
        _useRealisticData = true;
        _dataInitialized = true;
        _isLoading = false;
      });

      // عرض إحصائيات البيانات
      final stats = RealDataManager.getDataStatistics({
        'clients': _clients,
        'farms': _farms,
        'irrigations': _irrigations,
        'payments': _payments,
      });
      _showDataStatistics(stats);

      // التحقق من صحة البيانات
      await _validateDataIntegrity();

      debugPrint('✅ تم تهيئة البيانات الحقيقية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة البيانات الحقيقية: $e');
      // العودة إلى تحميل البيانات العادية
      _loadAllData();
    }
  }

  /// عرض إحصائيات البيانات
  void _showDataStatistics(Map<String, dynamic> stats) {
    if (!mounted) return;

    final message = '''
📊 تم تحميل البيانات الحقيقية بنجاح!

👥 العملاء: ${stats['clients_count']}
🌾 المزارع: ${stats['farms_count']}
💧 التسقيات: ${stats['irrigations_count']}
💰 المدفوعات: ${stats['payments_count']}

💵 إجمالي التكاليف: ${stats['total_irrigation_cost'].toStringAsFixed(2)} ريال
💳 إجمالي المدفوعات: ${stats['total_payments'].toStringAsFixed(2)} ريال
📈 الرصيد: ${stats['balance'].toStringAsFixed(2)} ريال
    ''';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'تفاصيل',
          textColor: Colors.white,
          onPressed: _showDetailedDataStatistics,
        ),
      ),
    );
  }

  /// عرض إحصائيات مفصلة للبيانات
  void _showDetailedDataStatistics() {
    if (!_useRealisticData) return;

    final stats = RealDataManager.getDataStatistics({
      'clients': _clients,
      'farms': _farms,
      'irrigations': _irrigations,
      'payments': _payments,
    });

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue),
            SizedBox(width: 8),
            Text('إحصائيات البيانات المفصلة'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatCard('العملاء', stats['clients_count'].toString(),
                    'عدد العملاء', Icons.people, Colors.blue),
                _buildStatCard('المزارع', stats['farms_count'].toString(),
                    'عدد المزارع', Icons.agriculture, Colors.green),
                _buildStatCard(
                    'التسقيات',
                    stats['irrigations_count'].toString(),
                    'عدد التسقيات',
                    Icons.water_drop,
                    Colors.cyan),
                _buildStatCard('المدفوعات', stats['payments_count'].toString(),
                    'عدد المدفوعات', Icons.payment, Colors.orange),
                const Divider(),
                _buildStatCard(
                    'إجمالي التكاليف',
                    '${stats['total_irrigation_cost'].toStringAsFixed(2)} ريال',
                    'تكاليف التسقية',
                    Icons.attach_money,
                    Colors.red),
                _buildStatCard(
                    'إجمالي المدفوعات',
                    '${stats['total_payments'].toStringAsFixed(2)} ريال',
                    'المبالغ المدفوعة',
                    Icons.account_balance_wallet,
                    Colors.green),
                _buildStatCard(
                    'الرصيد',
                    '${stats['balance'].toStringAsFixed(2)} ريال',
                    'الرصيد المتبقي',
                    Icons.account_balance,
                    Colors.purple),
                const Divider(),
                _buildStatCard(
                    'متوسط تكلفة التسقية',
                    '${stats['average_irrigation_cost'].toStringAsFixed(2)} ريال',
                    'متوسط التكلفة',
                    Icons.trending_up,
                    Colors.indigo),
                _buildStatCard(
                    'متوسط الدفعة',
                    '${stats['average_payment'].toStringAsFixed(2)} ريال',
                    'متوسط الدفعة',
                    Icons.trending_down,
                    Colors.teal),
                _buildStatCard(
                    'إجمالي الديزل',
                    '${stats['total_diesel'].toStringAsFixed(2)} لتر',
                    'استهلاك الوقود',
                    Icons.local_gas_station,
                    Colors.amber),
                _buildStatCard(
                    'إجمالي الساعات',
                    '${stats['total_hours'].toStringAsFixed(2)} ساعة',
                    'ساعات العمل',
                    Icons.access_time,
                    Colors.brown),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _regenerateRealisticData();
            },
            child: const Text('إعادة إنشاء البيانات'),
          ),
        ],
      ),
    );
  }

  /// إعادة إنشاء البيانات الحقيقية
  Future<void> _regenerateRealisticData() async {
    setState(() {
      _isLoading = true;
      _dataInitialized = false;
    });

    await _initializeRealisticData();

    _showSuccessMessage('تم إعادة إنشاء البيانات الحقيقية بنجاح!');
  }

  /// عرض حوار التصدير المتقدم
  Future<void> _showAdvancedExportDialog() async {
    if (!_reportGenerated || _reportData.isEmpty) {
      _showErrorMessage('يجب إنشاء التقرير أولاً');
      return;
    }

    await AdvancedExportService.showAdvancedExportDialog(
      context: context,
      reportTitle: _getEnhancedReportTitle(),
      reportType: _getReportTypeDisplayName(),
      startDate: _startDate,
      endDate: _endDate,
      reportData: _reportData,
    );
  }

  /// تنفيذ إصلاح البيانات الشامل
  Future<void> _performDataRepair() async {
    // عرض حوار تأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.build_circle, color: Colors.orange),
            SizedBox(width: 8),
            Text('إصلاح البيانات'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سيتم تنفيذ إصلاح شامل للبيانات يشمل:'),
            SizedBox(height: 10),
            Text('• إصلاح البيانات التالفة والناقصة'),
            Text('• إزالة البيانات المكررة'),
            Text('• تصحيح العلاقات بين البيانات'),
            Text('• تطبيع وتنظيم البيانات'),
            SizedBox(height: 10),
            Text(
              'هذه العملية قد تستغرق بعض الوقت. هل تريد المتابعة؟',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('بدء الإصلاح'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🔧 بدء إصلاح البيانات الشامل...');

      // تنفيذ الإصلاح الشامل
      final repairResults = await DataRepairService.performComprehensiveRepair(
        clients: _clients,
        farms: _farms,
        irrigations: _irrigations,
        payments: _payments,
      );

      if (repairResults['success'] == true) {
        // تحديث البيانات المحلية
        final repairedData = repairResults['repaired_data'];
        setState(() {
          _clients = repairedData['clients'];
          _farms = repairedData['farms'];
          _irrigations = repairedData['irrigations'];
          _payments = repairedData['payments'];
          _reportGenerated = false; // إعادة إنشاء التقرير
          _isLoading = false;
        });

        // عرض تقرير الإصلاح
        _showRepairReport(repairResults['repair_results']);

        _showSuccessMessage('تم إصلاح البيانات بنجاح!');
      } else {
        _showErrorMessage('حدث خطأ أثناء إصلاح البيانات');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إصلاح البيانات: $e');
      _showErrorMessage('حدث خطأ أثناء إصلاح البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض تقرير الإصلاح
  void _showRepairReport(Map<String, dynamic> repairResults) {
    final report = DataRepairService.generateRepairReport(
        {'repair_results': repairResults});

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.assessment, color: Colors.green),
            SizedBox(width: 8),
            Text('تقرير إصلاح البيانات'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // ملخص الإصلاح
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'ملخص الإصلاح',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      const SizedBox(height: 8),
                      Text(
                          'إجمالي العناصر المُصلحة: ${report['summary']['total_items_repaired']}'),
                      Text(
                          'البيانات المكررة المُزالة: ${report['summary']['duplicates_removed']}'),
                      Text(
                          'العلاقات المُصلحة: ${report['summary']['relationships_fixed']}'),
                      Text(
                          'البيانات المُطبعة: ${report['summary']['data_normalized']}'),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // تفاصيل الإصلاح
                const Text(
                  'تفاصيل الإصلاح',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),

                _buildRepairDetailCard(
                    'العملاء',
                    repairResults['clients_repaired'],
                    ['أسماء فارغة', 'أرقام هواتف غير صحيحة', 'عناوين مفقودة']),

                _buildRepairDetailCard(
                    'المزارع',
                    repairResults['farms_repaired'],
                    ['أسماء مزارع فارغة', 'مواقع مفقودة', 'مساحات غير صحيحة']),

                _buildRepairDetailCard(
                    'التسقيات', repairResults['irrigations_repaired'], [
                  'تكاليف غير صحيحة',
                  'استهلاك ديزل خاطئ',
                  'مدد غير منطقية'
                ]),

                _buildRepairDetailCard(
                    'المدفوعات',
                    repairResults['payments_repaired'],
                    ['مبالغ غير صحيحة', 'معرفات عملاء خاطئة', 'ملاحظات فارغة']),

                const SizedBox(height: 16),

                // التوصيات
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'التوصيات',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                      const SizedBox(height: 8),
                      ...((report['recommendations'] as List)
                          .map((rec) => Padding(
                                padding: const EdgeInsets.only(bottom: 4),
                                child: Text('• $rec',
                                    style: const TextStyle(fontSize: 12)),
                              ))),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _generateReport(); // إعادة إنشاء التقرير بالبيانات المُصلحة
            },
            child: const Text('إنشاء تقرير جديد'),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة تفاصيل الإصلاح
  Widget _buildRepairDetailCard(
      String title, int repairedCount, List<String> issues) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 16),
              const SizedBox(width: 6),
              Text(
                '$title: $repairedCount مُصلح',
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 13),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...issues.map((issue) => Text(
                '• $issue',
                style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
              )),
        ],
      ),
    );
  }

  /// بناء مؤشر حالة البيانات
  Widget _buildDataStatusIndicator() {
    final stats = RealDataManager.getDataStatistics({
      'clients': _clients,
      'farms': _farms,
      'irrigations': _irrigations,
      'payments': _payments,
    });

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green[400]!, Colors.green[600]!],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.verified, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'البيانات الحقيقية مُفعلة',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${stats['clients_count']} عميل • ${stats['farms_count']} مزرعة • ${stats['irrigations_count']} تسقية • ${stats['payments_count']} دفعة',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${stats['balance'].toStringAsFixed(0)} ريال',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم معلومات البيانات
  Widget _buildDataInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.storage, color: Colors.green),
              SizedBox(width: 8),
              Text(
                'مصدر البيانات',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'البيانات الحقيقية من قاعدة البيانات',
            style: TextStyle(fontSize: 14, color: Colors.green),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildDataCount('العملاء', _clients.length, Icons.people),
              const SizedBox(width: 16),
              _buildDataCount('المزارع', _farms.length, Icons.agriculture),
              const SizedBox(width: 16),
              _buildDataCount(
                  'التسقيات', _irrigations.length, Icons.water_drop),
              const SizedBox(width: 16),
              _buildDataCount('المدفوعات', _payments.length, Icons.payment),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عداد البيانات
  Widget _buildDataCount(String label, int count, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 16, color: Colors.green),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة نوع البيانات
  Widget _buildDataTypeCard(
    bool dataType,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    final isSelected = _useRealisticData == dataType;

    return GestureDetector(
      onTap: () {
        if (_useRealisticData != dataType) {
          setState(() {
            _useRealisticData = dataType;
            _reportGenerated = false;
          });

          if (dataType) {
            _initializeRealisticData();
          } else {
            _loadAllData();
          }
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? color.withAlpha((0.1 * 255).toInt())
              : Colors.grey[50],
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey.shade600,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isSelected ? color : Colors.grey[800],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                color:
                    isSelected ? color.withOpacity(0.8) : Colors.grey.shade600,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// عرض الإحصائيات التفاعلية المتقدمة
  Future<void> _showInteractiveStats() async {
    if (!_reportGenerated || _reportData.isEmpty) {
      _showErrorMessage('يجب إنشاء التقرير أولاً');
      return;
    }

    try {
      _showLoadingMessage('جاري حساب الإحصائيات المتقدمة...');

      // حساب الإحصائيات المتقدمة
      final advancedStats =
          InteractiveStatsHelper.calculateAdvancedPerformanceStats(
        irrigations: _irrigations,
        payments: _payments,
        clients: _clients,
        farms: _farms,
        startDate: _startDate,
        endDate: _endDate,
      );

      // حساب مؤشر الأداء الإجمالي
      final performanceIndex =
          InteractiveStatsHelper.calculateOverallPerformanceIndex(
        stats: advancedStats,
      );

      // إنشاء بيانات الرسوم البيانية
      final chartData = InteractiveStatsHelper.generateChartData(
        irrigations: _irrigations,
        payments: _payments,
        chartType: 'daily',
        startDate: _startDate,
        endDate: _endDate,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        // عرض الإحصائيات في حوار متقدم
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => _buildInteractiveStatsDialog(
            advancedStats,
            performanceIndex,
            chartData,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }
      _showErrorMessage('حدث خطأ في حساب الإحصائيات: $e');
    }
  }

  /// بناء حوار الإحصائيات التفاعلية
  Widget _buildInteractiveStatsDialog(
    Map<String, dynamic> stats,
    Map<String, dynamic> performanceIndex,
    List<Map<String, dynamic>> chartData,
  ) {
    return Dialog(
      insetPadding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.9,
        child: Column(
          children: [
            // رأس الحوار
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withAlpha((0.8 * 255).toInt())
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.analytics, color: Colors.white, size: 28),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'الإحصائيات التفاعلية المتقدمة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // محتوى الإحصائيات
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // مؤشر الأداء الإجمالي
                    _buildPerformanceIndexCard(performanceIndex),
                    const SizedBox(height: 20),

                    // الإحصائيات الرئيسية
                    _buildMainStatsGrid(stats),
                    const SizedBox(height: 20),

                    // إحصائيات العملاء المتقدمة
                    _buildTopClientsSection(stats),
                    const SizedBox(height: 20),

                    // إحصائيات المزارع المتقدمة
                    _buildTopFarmsSection(stats),
                    const SizedBox(height: 20),

                    // اتجاهات الأداء
                    _buildPerformanceTrendsSection(stats),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة مؤشر الأداء الإجمالي
  Widget _buildPerformanceIndexCard(Map<String, dynamic> performanceIndex) {
    final score = performanceIndex['score'] as double;
    final grade = performanceIndex['grade'] as String;
    final color = performanceIndex['color'] as Color;
    final recommendations = performanceIndex['recommendations'] as List<String>;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [color.withAlpha((0.1 * 255).toInt()), Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withAlpha((0.2 * 255).toInt()),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(Icons.speed, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'مؤشر الأداء الإجمالي',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        grade,
                        style: TextStyle(
                          fontSize: 16,
                          color: color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: color.withAlpha((0.2 * 255).toInt()),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${score.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value: score / 100,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            ),
            const SizedBox(height: 16),

            // التوصيات
            if (recommendations.isNotEmpty) ...[
              const Text(
                'توصيات التحسين:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...recommendations.map((recommendation) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        Icon(Icons.lightbulb_outline,
                            color: Colors.amber, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            recommendation,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  )),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء شبكة الإحصائيات الرئيسية
  Widget _buildMainStatsGrid(Map<String, dynamic> stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات الرئيسية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي التسقيات',
              '${stats['total_irrigations'] ?? 0}',
              '${ReportDisplayEnhancer.formatNumber(stats['total_irrigation_cost'] ?? 0)} ريال',
              Icons.water_drop,
              Colors.blue,
            ),
            _buildStatCard(
              'إجمالي المدفوعات',
              '${stats['total_payments'] ?? 0}',
              '${ReportDisplayEnhancer.formatNumber(stats['total_payment_amount'] ?? 0)} ريال',
              Icons.payment,
              Colors.green,
            ),
            _buildStatCard(
              'معدل التحصيل',
              '${(stats['collection_rate'] ?? 0).toStringAsFixed(1)}%',
              'من إجمالي التكاليف',
              Icons.pie_chart,
              (stats['collection_rate'] ?? 0) >= 80
                  ? Colors.green
                  : Colors.orange,
            ),
            _buildStatCard(
              'كفاءة العمليات',
              '${(stats['overall_efficiency'] ?? 0).toStringAsFixed(1)}',
              'ريال/ساعة',
              Icons.trending_up,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, String subtitle, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border(left: BorderSide(color: color, width: 4)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 12,
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم أفضل العملاء
  Widget _buildTopClientsSection(Map<String, dynamic> stats) {
    final topClients = stats['top_clients'] as List<dynamic>? ?? [];

    if (topClients.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أفضل العملاء',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        ...topClients.take(5).map((client) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.blue.withOpacity(0.2),
                  child: const Icon(Icons.person, color: Colors.blue),
                ),
                title: Text(client['name'] ?? 'غير محدد'),
                subtitle: Text('${client['total_irrigations']} تسقية'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${ReportDisplayEnhancer.formatNumber(client['total_irrigation_cost'] ?? 0)} ريال',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'الرصيد: ${ReportDisplayEnhancer.formatNumber(client['balance'] ?? 0)} ريال',
                      style: TextStyle(
                        fontSize: 12,
                        color: (client['balance'] ?? 0) >= 0
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  /// بناء قسم أفضل المزارع
  Widget _buildTopFarmsSection(Map<String, dynamic> stats) {
    final topFarms = stats['top_farms'] as List<dynamic>? ?? [];

    if (topFarms.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أكثر المزارع نشاطاً',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        ...topFarms.take(5).map((farm) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.green.withOpacity(0.2),
                  child: const Icon(Icons.agriculture, color: Colors.green),
                ),
                title: Text(farm['name'] ?? 'غير محدد'),
                subtitle: Text(farm['location'] ?? 'غير محدد'),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${farm['total_irrigations']} تسقية',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${ReportDisplayEnhancer.formatNumber(farm['total_cost'] ?? 0)} ريال',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  /// بناء قسم اتجاهات الأداء
  Widget _buildPerformanceTrendsSection(Map<String, dynamic> stats) {
    final peakIrrigationDay =
        stats['peak_irrigation_day'] as Map<String, dynamic>? ?? {};
    final peakPaymentDay =
        stats['peak_payment_day'] as Map<String, dynamic>? ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'اتجاهات الأداء',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(Icons.water_drop,
                          color: Colors.blue, size: 32),
                      const SizedBox(height: 8),
                      const Text(
                        'أكثر أيام التسقية نشاطاً',
                        style: TextStyle(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        peakIrrigationDay['date'] ?? 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        '${peakIrrigationDay['count'] ?? 0} تسقية',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(Icons.payment, color: Colors.green, size: 32),
                      const SizedBox(height: 8),
                      const Text(
                        'أكثر أيام المدفوعات نشاطاً',
                        style: TextStyle(fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        peakPaymentDay['date'] ?? 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                      Text(
                        '${peakPaymentDay['count'] ?? 0} دفعة',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء أعمدة جدول البيانات
  List<DataColumn> _buildDataTableColumns() {
    final data = _getFilteredReportData();
    if (data.isEmpty) return [];

    final firstItem = data.first;
    return firstItem.keys.map((key) {
      return DataColumn(
        label: Text(
          _getColumnDisplayName(key),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      );
    }).toList();
  }

  /// بناء صف جدول البيانات
  DataRow _buildDataTableRow(Map<String, dynamic> item) {
    return DataRow(
      cells: item.values.map((value) {
        return DataCell(
          Text(
            value?.toString() ?? '',
            style: const TextStyle(fontSize: 14),
          ),
        );
      }).toList(),
    );
  }

  /// إنشاء بيانات تجريبية للتقارير
  void _createSampleData() async {
    debugPrint('📊 إنشاء بيانات تجريبية للتقارير...');

    // عرض مؤشر التحميل
    _showLoadingMessage('جاري إنشاء البيانات التجريبية...');

    try {
      // إنشاء عملاء تجريبيين
      _clients = [
        ClientModel(
          id: 1,
          name: 'أحمد محمد',
          phone: '0501234567',
          address: 'الرياض - حي النرجس',
          createdAt: DateTime.now().subtract(const Duration(days: 60)),
          updatedAt: DateTime.now().subtract(const Duration(days: 60)),
          notes: 'عميل نشط ومخلص',
        ),
        ClientModel(
          id: 2,
          name: 'فاطمة علي',
          phone: '0502345678',
          address: 'الرياض - حي الربيع',
          createdAt: DateTime.now().subtract(const Duration(days: 45)),
          updatedAt: DateTime.now().subtract(const Duration(days: 45)),
          notes: 'عميل جديد',
        ),
        ClientModel(
          id: 3,
          name: 'محمد عبدالله',
          phone: '0503456789',
          address: 'الرياض - حي الورود',
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now().subtract(const Duration(days: 30)),
          notes: 'عميل منتظم',
        ),
        ClientModel(
          id: 4,
          name: 'سارة أحمد',
          phone: '0504567890',
          address: 'الرياض - حي الياسمين',
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          updatedAt: DateTime.now().subtract(const Duration(days: 20)),
          notes: 'عميل نشط',
        ),
        ClientModel(
          id: 5,
          name: 'علي حسن',
          phone: '0505678901',
          address: 'الرياض - حي الزهور',
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          updatedAt: DateTime.now().subtract(const Duration(days: 15)),
          notes: 'عميل جديد',
        ),
      ];

      // إنشاء مزارع تجريبية
      _farms = [
        FarmModel(
          id: 1,
          name: 'مزرعة النخيل',
          location: 'الرياض - طريق الملك فهد',
          area: 50.0,
          clientId: 1,
          createdAt: DateTime.now().subtract(const Duration(days: 60)),
          updatedAt: DateTime.now().subtract(const Duration(days: 60)),
          notes: 'مزرعة كبيرة ومتطورة - نخيل',
        ),
        FarmModel(
          id: 2,
          name: 'مزرعة الخضروات',
          location: 'الرياض - طريق الملك خالد',
          area: 25.0,
          clientId: 2,
          createdAt: DateTime.now().subtract(const Duration(days: 45)),
          updatedAt: DateTime.now().subtract(const Duration(days: 45)),
          notes: 'مزرعة متوسطة الحجم - خضروات',
        ),
        FarmModel(
          id: 3,
          name: 'مزرعة الفواكه',
          location: 'الرياض - طريق الملك عبدالله',
          area: 35.0,
          clientId: 3,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now().subtract(const Duration(days: 30)),
          notes: 'مزرعة متنوعة - فواكه',
        ),
        FarmModel(
          id: 4,
          name: 'مزرعة الحبوب',
          location: 'الرياض - طريق الملك سلمان',
          area: 40.0,
          clientId: 4,
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          updatedAt: DateTime.now().subtract(const Duration(days: 20)),
          notes: 'مزرعة تقليدية - حبوب',
        ),
        FarmModel(
          id: 5,
          name: 'مزرعة الزهور',
          location: 'الرياض - طريق الملك فيصل',
          area: 15.0,
          clientId: 5,
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          updatedAt: DateTime.now().subtract(const Duration(days: 15)),
          notes: 'مزرعة صغيرة ومتخصصة - زهور',
        ),
      ];

      // إنشاء تسقيات تجريبية
      _irrigations = [
        // تسقيات العميل الأول
        IrrigationModel(
          id: 1,
          clientId: 1,
          farmId: 1,
          startTime:
              DateTime.now().subtract(const Duration(days: 25, hours: 2)),
          endTime: DateTime.now().subtract(const Duration(days: 25, hours: 1)),
          duration: 60,
          cost: 150.0,
          dieselConsumption: 25.0,
          createdAt: DateTime.now().subtract(const Duration(days: 25)),
          updatedAt: DateTime.now().subtract(const Duration(days: 25)),
          notes: 'تسقية عادية - حالة جيدة',
        ),
        IrrigationModel(
          id: 2,
          clientId: 1,
          farmId: 1,
          startTime:
              DateTime.now().subtract(const Duration(days: 20, hours: 3)),
          endTime: DateTime.now().subtract(const Duration(days: 20, hours: 2)),
          duration: 60,
          cost: 150.0,
          dieselConsumption: 25.0,
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          updatedAt: DateTime.now().subtract(const Duration(days: 20)),
          notes: 'تسقية عادية - حالة ممتازة',
        ),
        IrrigationModel(
          id: 3,
          clientId: 1,
          farmId: 1,
          startTime:
              DateTime.now().subtract(const Duration(days: 15, hours: 4)),
          endTime: DateTime.now().subtract(const Duration(days: 15, hours: 3)),
          duration: 60,
          cost: 150.0,
          dieselConsumption: 25.0,
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          updatedAt: DateTime.now().subtract(const Duration(days: 15)),
          notes: 'تسقية عادية - حالة جيدة',
        ),

        // تسقيات العميل الثاني
        IrrigationModel(
          id: 4,
          clientId: 2,
          farmId: 2,
          startTime:
              DateTime.now().subtract(const Duration(days: 22, hours: 5)),
          endTime: DateTime.now().subtract(const Duration(days: 22, hours: 4)),
          duration: 45,
          cost: 120.0,
          dieselConsumption: 20.0,
          createdAt: DateTime.now().subtract(const Duration(days: 22)),
          updatedAt: DateTime.now().subtract(const Duration(days: 22)),
          notes: 'تسقية خفيفة - حالة جيدة',
        ),
        IrrigationModel(
          id: 5,
          clientId: 2,
          farmId: 2,
          startTime:
              DateTime.now().subtract(const Duration(days: 17, hours: 6)),
          endTime: DateTime.now().subtract(const Duration(days: 17, hours: 5)),
          duration: 45,
          cost: 120.0,
          dieselConsumption: 20.0,
          createdAt: DateTime.now().subtract(const Duration(days: 17)),
          updatedAt: DateTime.now().subtract(const Duration(days: 17)),
          notes: 'تسقية خفيفة - حالة ممتازة',
        ),

        // تسقيات العميل الثالث
        IrrigationModel(
          id: 6,
          clientId: 3,
          farmId: 3,
          startTime:
              DateTime.now().subtract(const Duration(days: 18, hours: 7)),
          endTime: DateTime.now().subtract(const Duration(days: 18, hours: 6)),
          duration: 75,
          cost: 180.0,
          dieselConsumption: 30.0,
          createdAt: DateTime.now().subtract(const Duration(days: 18)),
          updatedAt: DateTime.now().subtract(const Duration(days: 18)),
          notes: 'تسقية مكثفة - حالة جيدة',
        ),
        IrrigationModel(
          id: 7,
          clientId: 3,
          farmId: 3,
          startTime:
              DateTime.now().subtract(const Duration(days: 12, hours: 8)),
          endTime: DateTime.now().subtract(const Duration(days: 12, hours: 7)),
          duration: 75,
          cost: 180.0,
          dieselConsumption: 30.0,
          createdAt: DateTime.now().subtract(const Duration(days: 12)),
          updatedAt: DateTime.now().subtract(const Duration(days: 12)),
          notes: 'تسقية مكثفة - حالة ممتازة',
        ),

        // تسقيات العميل الرابع
        IrrigationModel(
          id: 8,
          clientId: 4,
          farmId: 4,
          startTime:
              DateTime.now().subtract(const Duration(days: 16, hours: 9)),
          endTime: DateTime.now().subtract(const Duration(days: 16, hours: 8)),
          duration: 90,
          cost: 200.0,
          dieselConsumption: 35.0,
          createdAt: DateTime.now().subtract(const Duration(days: 16)),
          updatedAt: DateTime.now().subtract(const Duration(days: 16)),
          notes: 'تسقية طويلة - حالة جيدة',
        ),
        IrrigationModel(
          id: 9,
          clientId: 4,
          farmId: 4,
          startTime:
              DateTime.now().subtract(const Duration(days: 10, hours: 10)),
          endTime: DateTime.now().subtract(const Duration(days: 10, hours: 9)),
          duration: 90,
          cost: 200.0,
          dieselConsumption: 35.0,
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          updatedAt: DateTime.now().subtract(const Duration(days: 10)),
          notes: 'تسقية طويلة - حالة ممتازة',
        ),

        // تسقيات العميل الخامس
        IrrigationModel(
          id: 10,
          clientId: 5,
          farmId: 5,
          startTime:
              DateTime.now().subtract(const Duration(days: 8, hours: 11)),
          endTime: DateTime.now().subtract(const Duration(days: 8, hours: 10)),
          duration: 30,
          cost: 80.0,
          dieselConsumption: 15.0,
          createdAt: DateTime.now().subtract(const Duration(days: 8)),
          updatedAt: DateTime.now().subtract(const Duration(days: 8)),
          notes: 'تسقية خفيفة - حالة جيدة',
        ),
        IrrigationModel(
          id: 11,
          clientId: 5,
          farmId: 5,
          startTime:
              DateTime.now().subtract(const Duration(days: 5, hours: 12)),
          endTime: DateTime.now().subtract(const Duration(days: 5, hours: 11)),
          duration: 30,
          cost: 80.0,
          dieselConsumption: 15.0,
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          updatedAt: DateTime.now().subtract(const Duration(days: 5)),
          notes: 'تسقية خفيفة - حالة ممتازة',
        ),

        // تسقيات إضافية للفترة الأخيرة
        IrrigationModel(
          id: 12,
          clientId: 1,
          farmId: 1,
          startTime:
              DateTime.now().subtract(const Duration(days: 3, hours: 13)),
          endTime: DateTime.now().subtract(const Duration(days: 3, hours: 12)),
          duration: 60,
          cost: 150.0,
          dieselConsumption: 25.0,
          createdAt: DateTime.now().subtract(const Duration(days: 3)),
          updatedAt: DateTime.now().subtract(const Duration(days: 3)),
          notes: 'تسقية عادية - حالة جيدة',
        ),
        IrrigationModel(
          id: 13,
          clientId: 2,
          farmId: 2,
          startTime:
              DateTime.now().subtract(const Duration(days: 2, hours: 14)),
          endTime: DateTime.now().subtract(const Duration(days: 2, hours: 13)),
          duration: 45,
          cost: 120.0,
          dieselConsumption: 20.0,
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          updatedAt: DateTime.now().subtract(const Duration(days: 2)),
          notes: 'تسقية خفيفة - حالة ممتازة',
        ),
        IrrigationModel(
          id: 14,
          clientId: 3,
          farmId: 3,
          startTime:
              DateTime.now().subtract(const Duration(days: 1, hours: 15)),
          endTime: DateTime.now().subtract(const Duration(days: 1, hours: 14)),
          duration: 75,
          cost: 180.0,
          dieselConsumption: 30.0,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
          notes: 'تسقية مكثفة - حالة جيدة',
        ),
      ];

      // إنشاء مدفوعات تجريبية
      _payments = [
        // مدفوعات العميل الأول
        PaymentModel(
          id: 1,
          clientId: 1,
          type: 'cash',
          amount: 300.0,
          cashboxId: 1,
          paymentDate: DateTime.now().subtract(const Duration(days: 20)),
          createdAt: DateTime.now().subtract(const Duration(days: 20)),
          updatedAt: DateTime.now().subtract(const Duration(days: 20)),
          notes: 'دفعة جزئية - حالة ممتازة',
        ),
        PaymentModel(
          id: 2,
          clientId: 1,
          type: 'cash',
          amount: 150.0,
          cashboxId: 1,
          paymentDate: DateTime.now().subtract(const Duration(days: 10)),
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          updatedAt: DateTime.now().subtract(const Duration(days: 10)),
          notes: 'دفعة كاملة - حالة ممتازة',
        ),

        // مدفوعات العميل الثاني
        PaymentModel(
          id: 3,
          clientId: 2,
          type: 'cash',
          amount: 240.0,
          cashboxId: 1,
          paymentDate: DateTime.now().subtract(const Duration(days: 15)),
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          updatedAt: DateTime.now().subtract(const Duration(days: 15)),
          notes: 'دفعة كاملة - حالة ممتازة',
        ),

        // مدفوعات العميل الثالث
        PaymentModel(
          id: 4,
          clientId: 3,
          type: 'cash',
          amount: 180.0,
          cashboxId: 1,
          paymentDate: DateTime.now().subtract(const Duration(days: 12)),
          createdAt: DateTime.now().subtract(const Duration(days: 12)),
          updatedAt: DateTime.now().subtract(const Duration(days: 12)),
          notes: 'دفعة جزئية - حالة جيدة',
        ),
        PaymentModel(
          id: 5,
          clientId: 3,
          type: 'cash',
          amount: 180.0,
          cashboxId: 1,
          paymentDate: DateTime.now().subtract(const Duration(days: 5)),
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          updatedAt: DateTime.now().subtract(const Duration(days: 5)),
          notes: 'دفعة كاملة - حالة ممتازة',
        ),

        // مدفوعات العميل الرابع
        PaymentModel(
          id: 6,
          clientId: 4,
          type: 'cash',
          amount: 200.0,
          cashboxId: 1,
          paymentDate: DateTime.now().subtract(const Duration(days: 8)),
          createdAt: DateTime.now().subtract(const Duration(days: 8)),
          updatedAt: DateTime.now().subtract(const Duration(days: 8)),
          notes: 'دفعة كاملة - حالة ممتازة',
        ),

        // مدفوعات العميل الخامس
        PaymentModel(
          id: 7,
          clientId: 5,
          type: 'cash',
          amount: 160.0,
          cashboxId: 1,
          paymentDate: DateTime.now().subtract(const Duration(days: 3)),
          createdAt: DateTime.now().subtract(const Duration(days: 3)),
          updatedAt: DateTime.now().subtract(const Duration(days: 3)),
          notes: 'دفعة كاملة - حالة ممتازة',
        ),
      ];

      debugPrint('✅ تم إنشاء البيانات التجريبية:');
      debugPrint('   - ${_clients.length} عميل');
      debugPrint('   - ${_farms.length} مزرعة');
      debugPrint('   - ${_irrigations.length} تسقية');
      debugPrint('   - ${_payments.length} دفعة');

      setState(() {
        _dataInitialized = true;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showSuccessMessage('تم إنشاء البيانات التجريبية بنجاح!');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء البيانات التجريبية: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        _showErrorMessage('حدث خطأ في إنشاء البيانات التجريبية: $e');
      }
    }
  }
}

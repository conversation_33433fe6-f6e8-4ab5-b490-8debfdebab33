import 'package:flutter/material.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';

/// خدمة تنظيف البيانات
class DataCleanupService {
  /// تنظيف البيانات العشوائية والوهمية
  static Future<Map<String, int>> cleanupRandomData({
    required List<ClientModel> clients,
    required List<FarmModel> farms,
    required List<IrrigationModel> irrigations,
    required List<PaymentModel> payments,
    required List<CashboxModel> cashboxes,
    required List<ClientAccountModel> accounts,
  }) async {
    try {
      debugPrint('🔄 بدء تنظيف البيانات العشوائية...');

      final cleanupResults = <String, int>{};

      // تنظيف العملاء
      final cleanedClients = _cleanupClients(clients);
      cleanupResults['clients_removed'] =
          clients.length - cleanedClients.length;

      // تنظيف المزارع
      final cleanedFarms = _cleanupFarms(farms);
      cleanupResults['farms_removed'] = farms.length - cleanedFarms.length;

      // تنظيف التسقيات
      final cleanedIrrigations = _cleanupIrrigations(irrigations);
      cleanupResults['irrigations_removed'] =
          irrigations.length - cleanedIrrigations.length;

      // تنظيف المدفوعات
      final cleanedPayments = _cleanupPayments(payments);
      cleanupResults['payments_removed'] =
          payments.length - cleanedPayments.length;

      // تنظيف الصناديق
      final cleanedCashboxes = _cleanupCashboxes(cashboxes);
      cleanupResults['cashboxes_removed'] =
          cashboxes.length - cleanedCashboxes.length;

      // تنظيف الحسابات
      final cleanedAccounts = _cleanupAccounts(accounts);
      cleanupResults['accounts_removed'] =
          accounts.length - cleanedAccounts.length;

      debugPrint('✅ تم تنظيف البيانات بنجاح');
      debugPrint('📊 نتائج التنظيف: $cleanupResults');

      return cleanupResults;
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات: $e');
      rethrow;
    }
  }

  /// تنظيف العملاء
  static List<ClientModel> _cleanupClients(List<ClientModel> clients) {
    return clients.where((client) {
      // إزالة العملاء بأسماء وهمية أو عشوائية
      final randomNames = [
        'test',
        'test1',
        'test2',
        'test3',
        'test4',
        'test5',
        'عميل',
        'عميل تجريبي',
        'عميل وهمي',
        'عميل عشوائي',
        'client',
        'client1',
        'client2',
        'client3',
        'client4',
        'client5',
        'user',
        'user1',
        'user2',
        'user3',
        'user4',
        'user5',
        'demo',
        'demo1',
        'demo2',
        'demo3',
        'demo4',
        'demo5',
        'sample',
        'sample1',
        'sample2',
        'sample3',
        'sample4',
        'sample5',
        'example',
        'example1',
        'example2',
        'example3',
        'example4',
        'example5',
        'fake',
        'fake1',
        'fake2',
        'fake3',
        'fake4',
        'fake5',
        'dummy',
        'dummy1',
        'dummy2',
        'dummy3',
        'dummy4',
        'dummy5',
        'temp',
        'temp1',
        'temp2',
        'temp3',
        'temp4',
        'temp5',
        'random',
        'random1',
        'random2',
        'random3',
        'random4',
        'random5',
      ];

      // إزالة العملاء بأرقام هواتف وهمية
      final fakePhones = [
        '0000000000',
        '1111111111',
        '2222222222',
        '3333333333',
        '4444444444',
        '5555555555',
        '6666666666',
        '7777777777',
        '8888888888',
        '9999999999',
        '1234567890',
        '0987654321',
        '000000000',
        '111111111',
        '222222222',
        '333333333',
        '444444444',
        '555555555',
        '666666666',
        '777777777',
        '888888888',
        '999999999',
        '123456789',
        '987654321',
      ];

      // إزالة العملاء بعناوين وهمية
      final fakeAddresses = [
        'test address',
        'fake address',
        'dummy address',
        'sample address',
        'example address',
        'random address',
        'temp address',
        'demo address',
        'عنوان تجريبي',
        'عنوان وهمي',
        'عنوان عشوائي',
        'عنوان مؤقت',
        'عنوان تجريبي',
        'عنوان وهمي',
        'عنوان عشوائي',
        'عنوان مؤقت',
      ];

      final name = client.name.toLowerCase().trim();
      final phone = client.phone.trim();
      final address = client.address.toLowerCase().trim();

      // التحقق من الأسماء الوهمية
      if (randomNames.contains(name)) {
        debugPrint('🗑️ إزالة عميل بأسم وهمي: ${client.name}');
        return false;
      }

      // التحقق من أرقام الهواتف الوهمية
      if (fakePhones.contains(phone)) {
        debugPrint('🗑️ إزالة عميل برقم هاتف وهمي: ${client.name}');
        return false;
      }

      // التحقق من العناوين الوهمية
      if (fakeAddresses.contains(address)) {
        debugPrint('🗑️ إزالة عميل بعنوان وهمي: ${client.name}');
        return false;
      }

      // التحقق من الأسماء الفارغة أو القصيرة جداً
      if (name.isEmpty || name.length < 2) {
        debugPrint('🗑️ إزالة عميل بأسم فارغ أو قصير: ${client.name}');
        return false;
      }

      // التحقق من أرقام الهواتف الفارغة أو غير الصحيحة
      if (phone.isEmpty || phone.length < 8) {
        debugPrint('🗑️ إزالة عميل برقم هاتف فارغ أو غير صحيح: ${client.name}');
        return false;
      }

      return true;
    }).toList();
  }

  /// تنظيف المزارع
  static List<FarmModel> _cleanupFarms(List<FarmModel> farms) {
    return farms.where((farm) {
      // إزالة المزارع بأسماء وهمية أو عشوائية
      final randomNames = [
        'test farm',
        'test1 farm',
        'test2 farm',
        'test3 farm',
        'test4 farm',
        'test5 farm',
        'مزرعة تجريبية',
        'مزرعة وهمية',
        'مزرعة عشوائية',
        'مزرعة مؤقتة',
        'farm test',
        'farm1 test',
        'farm2 test',
        'farm3 test',
        'farm4 test',
        'farm5 test',
        'demo farm',
        'demo1 farm',
        'demo2 farm',
        'demo3 farm',
        'demo4 farm',
        'demo5 farm',
        'sample farm',
        'sample1 farm',
        'sample2 farm',
        'sample3 farm',
        'sample4 farm',
        'sample5 farm',
        'example farm',
        'example1 farm',
        'example2 farm',
        'example3 farm',
        'example4 farm',
        'example5 farm',
        'fake farm',
        'fake1 farm',
        'fake2 farm',
        'fake3 farm',
        'fake4 farm',
        'fake5 farm',
        'dummy farm',
        'dummy1 farm',
        'dummy2 farm',
        'dummy3 farm',
        'dummy4 farm',
        'dummy5 farm',
        'temp farm',
        'temp1 farm',
        'temp2 farm',
        'temp3 farm',
        'temp4 farm',
        'temp5 farm',
        'random farm',
        'random1 farm',
        'random2 farm',
        'random3 farm',
        'random4 farm',
        'random5 farm',
      ];

      // إزالة المزارع بمواقع وهمية
      final fakeLocations = [
        'test location',
        'fake location',
        'dummy location',
        'sample location',
        'example location',
        'random location',
        'temp location',
        'demo location',
        'موقع تجريبي',
        'موقع وهمي',
        'موقع عشوائي',
        'موقع مؤقت',
      ];

      final name = farm.name.toLowerCase().trim();
      final location = farm.location.toLowerCase().trim();

      // التحقق من الأسماء الوهمية
      if (randomNames.contains(name)) {
        debugPrint('🗑️ إزالة مزرعة بأسم وهمي: ${farm.name}');
        return false;
      }

      // التحقق من المواقع الوهمية
      if (fakeLocations.contains(location)) {
        debugPrint('🗑️ إزالة مزرعة بموقع وهمي: ${farm.name}');
        return false;
      }

      // التحقق من الأسماء الفارغة أو القصيرة جداً
      if (name.isEmpty || name.length < 2) {
        debugPrint('🗑️ إزالة مزرعة بأسم فارغ أو قصير: ${farm.name}');
        return false;
      }

      // التحقق من المساحات غير المنطقية
      if (farm.size <= 0 || farm.size > 10000) {
        debugPrint('🗑️ إزالة مزرعة بمساحة غير منطقية: ${farm.name}');
        return false;
      }

      return true;
    }).toList();
  }

  /// تنظيف التسقيات
  static List<IrrigationModel> _cleanupIrrigations(
      List<IrrigationModel> irrigations) {
    return irrigations.where((irrigation) {
      // التحقق من المدة غير المنطقية
      if (irrigation.duration <= 0 || irrigation.duration > 24) {
        debugPrint('🗑️ إزالة تسقية بمدة غير منطقية: ${irrigation.farmName}');
        return false;
      }

      // التحقق من استهلاك الديزل غير المنطقي
      if (irrigation.dieselConsumption < 0 ||
          irrigation.dieselConsumption > 1000) {
        debugPrint(
            '🗑️ إزالة تسقية باستهلاك ديزل غير منطقي: ${irrigation.farmName}');
        return false;
      }

      // التحقق من التكلفة غير المنطقية
      if (irrigation.cost < 0 || irrigation.cost > 100000) {
        debugPrint('🗑️ إزالة تسقية بتكلفة غير منطقية: ${irrigation.farmName}');
        return false;
      }

      // التحقق من التاريخ غير المنطقي
      if (irrigation.date != null) {
        final now = DateTime.now();
        final irrigationDate = irrigation.date!;

        // إزالة التسقيات في المستقبل أو قبل 10 سنوات
        if (irrigationDate.isAfter(now) ||
            irrigationDate.isBefore(now.subtract(const Duration(days: 3650)))) {
          debugPrint(
              '🗑️ إزالة تسقية بتاريخ غير منطقي: ${irrigation.farmName}');
          return false;
        }
      }

      return true;
    }).toList();
  }

  /// تنظيف المدفوعات
  static List<PaymentModel> _cleanupPayments(List<PaymentModel> payments) {
    return payments.where((payment) {
      // التحقق من المبلغ غير المنطقي
      if (payment.amount <= 0 || payment.amount > 1000000) {
        debugPrint('🗑️ إزالة دفعة بمبلغ غير منطقي: ${payment.clientName}');
        return false;
      }

      // التحقق من التاريخ غير المنطقي
      if (payment.date != null) {
        final now = DateTime.now();
        final paymentDate = payment.date!;

        // إزالة المدفوعات في المستقبل أو قبل 10 سنوات
        if (paymentDate.isAfter(now) ||
            paymentDate.isBefore(now.subtract(const Duration(days: 3650)))) {
          debugPrint('🗑️ إزالة دفعة بتاريخ غير منطقي: ${payment.clientName}');
          return false;
        }
      }

      // التحقق من الوصف الفارغ أو الوهمي
      final description = payment.description.toLowerCase().trim();
      final fakeDescriptions = [
        'test payment',
        'fake payment',
        'dummy payment',
        'sample payment',
        'example payment',
        'random payment',
        'temp payment',
        'demo payment',
        'دفعة تجريبية',
        'دفعة وهمية',
        'دفعة عشوائية',
        'دفعة مؤقتة',
      ];

      if (fakeDescriptions.contains(description)) {
        debugPrint('🗑️ إزالة دفعة بوصف وهمي: ${payment.clientName}');
        return false;
      }

      return true;
    }).toList();
  }

  /// تنظيف الصناديق
  static List<CashboxModel> _cleanupCashboxes(List<CashboxModel> cashboxes) {
    return cashboxes.where((cashbox) {
      // إزالة الصناديق بأسماء وهمية أو عشوائية
      final randomNames = [
        'test cashbox',
        'test1 cashbox',
        'test2 cashbox',
        'test3 cashbox',
        'test4 cashbox',
        'test5 cashbox',
        'صندوق تجريبي',
        'صندوق وهمي',
        'صندوق عشوائي',
        'صندوق مؤقت',
        'cashbox test',
        'cashbox1 test',
        'cashbox2 test',
        'cashbox3 test',
        'cashbox4 test',
        'cashbox5 test',
        'demo cashbox',
        'demo1 cashbox',
        'demo2 cashbox',
        'demo3 cashbox',
        'demo4 cashbox',
        'demo5 cashbox',
        'sample cashbox',
        'sample1 cashbox',
        'sample2 cashbox',
        'sample3 cashbox',
        'sample4 cashbox',
        'sample5 cashbox',
        'example cashbox',
        'example1 cashbox',
        'example2 cashbox',
        'example3 cashbox',
        'example4 cashbox',
        'example5 cashbox',
        'fake cashbox',
        'fake1 cashbox',
        'fake2 cashbox',
        'fake3 cashbox',
        'fake4 cashbox',
        'fake5 cashbox',
        'dummy cashbox',
        'dummy1 cashbox',
        'dummy2 cashbox',
        'dummy3 cashbox',
        'dummy4 cashbox',
        'dummy5 cashbox',
        'temp cashbox',
        'temp1 cashbox',
        'temp2 cashbox',
        'temp3 cashbox',
        'temp4 cashbox',
        'temp5 cashbox',
        'random cashbox',
        'random1 cashbox',
        'random2 cashbox',
        'random3 cashbox',
        'random4 cashbox',
        'random5 cashbox',
      ];

      final name = cashbox.name.toLowerCase().trim();

      // التحقق من الأسماء الوهمية
      if (randomNames.contains(name)) {
        debugPrint('🗑️ إزالة صندوق بأسم وهمي: ${cashbox.name}');
        return false;
      }

      // التحقق من الأسماء الفارغة أو القصيرة جداً
      if (name.isEmpty || name.length < 2) {
        debugPrint('🗑️ إزالة صندوق بأسم فارغ أو قصير: ${cashbox.name}');
        return false;
      }

      // التحقق من الرصيد غير المنطقي
      if (cashbox.balance < -1000000 || cashbox.balance > 1000000) {
        debugPrint('🗑️ إزالة صندوق برصيد غير منطقي: ${cashbox.name}');
        return false;
      }

      return true;
    }).toList();
  }

  /// تنظيف الحسابات
  static List<ClientAccountModel> _cleanupAccounts(
      List<ClientAccountModel> accounts) {
    return accounts.where((account) {
      // التحقق من الرصيد غير المنطقي
      if (account.balance < -1000000 || account.balance > 1000000) {
        debugPrint('🗑️ إزالة حساب برصيد غير منطقي: ${account.clientName}');
        return false;
      }

      // التحقق من اسم العميل الفارغ
      if (account.clientName.trim().isEmpty) {
        debugPrint('🗑️ إزالة حساب باسم عميل فارغ');
        return false;
      }

      return true;
    }).toList();
  }

  /// إزالة الميزات المتعلقة بالأرباح
  static Future<void> removeProfitFeatures() async {
    try {
      debugPrint('🔄 إزالة الميزات المتعلقة بالأرباح...');

      // هنا يمكن إضافة منطق إزالة الميزات المتعلقة بالأرباح
      // مثل حذف الحقول أو الجداول المتعلقة بحسابات الأرباح

      debugPrint('✅ تم إزالة الميزات المتعلقة بالأرباح بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إزالة الميزات المتعلقة بالأرباح: $e');
      rethrow;
    }
  }

  /// تنظيف الحقول غير المطلوبة
  static Future<void> cleanupUnnecessaryFields() async {
    try {
      debugPrint('🔄 تنظيف الحقول غير المطلوبة...');

      // هنا يمكن إضافة منطق تنظيف الحقول غير المطلوبة
      // مثل إزالة الحقول المتعلقة بالأرباح أو الإحصائيات غير المطلوبة

      debugPrint('✅ تم تنظيف الحقول غير المطلوبة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الحقول غير المطلوبة: $e');
      rethrow;
    }
  }

  /// إنشاء تقرير تنظيف البيانات
  static Map<String, dynamic> generateCleanupReport(
      Map<String, int> cleanupResults) {
    final totalRemoved =
        cleanupResults.values.fold(0, (sum, count) => sum + count);

    return {
      'summary': {
        'total_records_removed': totalRemoved,
        'cleanup_date': DateTime.now().toIso8601String(),
        'status': totalRemoved > 0 ? 'تم التنظيف' : 'لا توجد بيانات للتنظيف',
      },
      'details': cleanupResults,
      'recommendations': [
        'مراجعة البيانات المتبقية للتأكد من صحتها',
        'إنشاء نسخة احتياطية قبل إجراء أي تنظيف إضافي',
        'تحديث البيانات بانتظام لتجنب تراكم البيانات الوهمية',
      ],
    };
  }
}

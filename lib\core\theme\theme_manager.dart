import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مدير الثيم لإدارة الوضع المظلم والفاتح
class ThemeManager extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';

  ThemeMode _themeMode = ThemeMode.light;

  ThemeMode get themeMode => _themeMode;

  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isLightMode => _themeMode == ThemeMode.light;
  bool get isSystemMode => _themeMode == ThemeMode.system;

  /// تهيئة مدير الثيم
  static Future<ThemeManager> initialize() async {
    final manager = ThemeManager();
    await manager._loadThemeMode();
    return manager;
  }

  /// تحميل وضع الثيم المحفوظ
  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt(_themeKey) ?? 0;
      _themeMode = ThemeMode.values[themeIndex];
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل وضع الثيم: $e');
    }
  }

  /// حفظ وضع الثيم
  Future<void> _saveThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, _themeMode.index);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ وضع الثيم: $e');
    }
  }

  /// تبديل إلى الوضع المظلم
  Future<void> setDarkMode() async {
    _themeMode = ThemeMode.dark;
    await _saveThemeMode();
    notifyListeners();
  }

  /// تبديل إلى الوضع الفاتح
  Future<void> setLightMode() async {
    _themeMode = ThemeMode.light;
    await _saveThemeMode();
    notifyListeners();
  }

  /// تبديل إلى وضع النظام
  Future<void> setSystemMode() async {
    _themeMode = ThemeMode.system;
    await _saveThemeMode();
    notifyListeners();
  }

  /// تبديل الوضع (فاتح ↔ مظلم)
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setDarkMode();
    } else {
      await setLightMode();
    }
  }

  /// الحصول على وصف الوضع الحالي
  String getCurrentThemeDescription() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'الوضع الفاتح';
      case ThemeMode.dark:
        return 'الوضع المظلم';
      case ThemeMode.system:
        return 'حسب النظام';
    }
  }

  /// الحصول على أيقونة الوضع الحالي
  IconData getCurrentThemeIcon() {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}

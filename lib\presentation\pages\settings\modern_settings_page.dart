import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/theme/theme_manager.dart';
import 'package:untitled/core/services/backup_restore_service.dart';
import 'package:untitled/core/services/advanced_export_service.dart';
import 'package:untitled/presentation/pages/settings/backup_management_page.dart';
import 'package:untitled/presentation/pages/settings/advanced_notifications_settings_page.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';

/// صفحة الإعدادات العصرية
class ModernSettingsPage extends StatefulWidget {
  const ModernSettingsPage({super.key});

  @override
  State<ModernSettingsPage> createState() => _ModernSettingsPageState();
}

class _ModernSettingsPageState extends State<ModernSettingsPage> {
  bool _notificationsEnabled = true;
  bool _autoBackup = false;
  bool _darkMode = false;
  bool _localNotificationsEnabled = false;
  String _language = 'ar';
  double _irrigationPrice = 3000.0;
  double _dieselRate = 10.0; // لتر لكل ساعة
  bool _isExporting = false;
  bool _isImporting = false;
  bool _isBackingUp = false;

  @override
  void initState() {
    super.initState();
    // تم حذف استدعاء دوال الإشعارات المحلية لعدم وجودها في BackupService
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildProfileSection(),
                  const SizedBox(height: 24),
                  _buildSettingsSection(
                    title: 'إعدادات التسقية',
                    icon: Icons.water_drop,
                    color: Colors.blue,
                    children: [
                      _buildSliderSetting(
                        title: 'سعر ساعة التسقية',
                        subtitle: '${_irrigationPrice.toInt()} ريال/ساعة',
                        value: _irrigationPrice,
                        min: 1000,
                        max: 10000,
                        divisions: 18,
                        onChanged: (value) =>
                            setState(() => _irrigationPrice = value),
                      ),
                      _buildSliderSetting(
                        title: 'معدل استهلاك الديزل',
                        subtitle: '${_dieselRate.toInt()} لتر/ساعة',
                        value: _dieselRate,
                        min: 5,
                        max: 20,
                        divisions: 15,
                        onChanged: (value) =>
                            setState(() => _dieselRate = value),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSettingsSection(
                    title: 'إعدادات النظام',
                    icon: Icons.settings,
                    color: Colors.green,
                    children: [
                      _buildSwitchSetting(
                        title: 'الإشعارات',
                        subtitle: 'تلقي إشعارات التطبيق',
                        value: _notificationsEnabled,
                        onChanged: (value) =>
                            setState(() => _notificationsEnabled = value),
                      ),
                      _buildSwitchSetting(
                        title: 'النسخ الاحتياطي التلقائي',
                        subtitle: 'نسخ احتياطي يومي للبيانات',
                        value: _autoBackup,
                        onChanged: (value) =>
                            setState(() => _autoBackup = value),
                      ),
                      _buildSwitchSetting(
                        title: 'الوضع المظلم',
                        subtitle: 'تفعيل الثيم المظلم',
                        value: _darkMode,
                        onChanged: (value) => setState(() => _darkMode = value),
                      ),
                      _buildDropdownSetting(
                        title: 'اللغة',
                        subtitle: _language == 'ar' ? 'العربية' : 'English',
                        value: _language,
                        items: const [
                          {'value': 'ar', 'label': 'العربية'},
                          {'value': 'en', 'label': 'English'},
                        ],
                        onChanged: (value) =>
                            setState(() => _language = value!),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSettingsSection(
                    title: 'الإشعارات المتقدمة',
                    icon: Icons.notifications_active,
                    color: Colors.indigo,
                    children: [
                      _buildActionSetting(
                        title: 'إعدادات الإشعارات المتقدمة',
                        subtitle: 'تخصيص الصوت والاهتزاز وساعات الهدوء',
                        icon: Icons.tune,
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const AdvancedNotificationsSettingsPage(),
                          ),
                        ),
                      ),
                      _buildActionSetting(
                        title: 'اختبار الإشعارات',
                        subtitle: 'اختبار نظام الإشعارات والتأكد من عمله',
                        icon: Icons.notifications_active,
                        onTap: () => Navigator.pushNamed(
                          context,
                          '/notification-test',
                        ),
                      ),
                      _buildSwitchSetting(
                        title: 'الإشعارات المحلية',
                        subtitle: 'إشعارات تعمل في الخلفية',
                        value: _localNotificationsEnabled,
                        onChanged: (value) =>
                            setState(() => _localNotificationsEnabled = value),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSettingsSection(
                    title: 'البيانات والنسخ الاحتياطي',
                    icon: Icons.backup,
                    color: Colors.orange,
                    children: [
                      _buildActionSetting(
                        title: 'إنشاء نسخة احتياطية',
                        subtitle: 'حفظ نسخة من جميع البيانات',
                        icon: Icons.cloud_upload,
                        onTap: _createBackup,
                      ),
                      _buildActionSetting(
                        title: 'استعادة البيانات',
                        subtitle: 'استعادة من نسخة احتياطية',
                        icon: Icons.cloud_download,
                        onTap: _restoreBackup,
                      ),
                      _buildActionSetting(
                        title: 'تصدير البيانات',
                        subtitle: 'تصدير البيانات كملف Excel',
                        icon: Icons.file_download,
                        onTap: _exportData,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSettingsSection(
                    title: 'المساعدة والدعم',
                    icon: Icons.help,
                    color: Colors.purple,
                    children: [
                      _buildActionSetting(
                        title: 'مركز المساعدة',
                        subtitle: 'دليل الاستخدام والأسئلة الشائعة',
                        icon: Icons.help_center,
                        onTap: () =>
                            Navigator.pushNamed(context, '/help-center'),
                      ),
                      _buildActionSetting(
                        title: 'البدء السريع',
                        subtitle: 'جولة تعريفية للمبتدئين',
                        icon: Icons.rocket_launch,
                        onTap: () =>
                            Navigator.pushNamed(context, '/quick-start'),
                      ),
                      _buildActionSetting(
                        title: 'تواصل معنا',
                        subtitle: 'الدعم الفني والاستفسارات',
                        icon: Icons.contact_support,
                        onTap: _contactSupport,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSettingsSection(
                    title: 'النسخ الاحتياطي والاستعادة',
                    icon: Icons.backup,
                    color: Colors.purple,
                    children: [
                      _buildActionSetting(
                        title: 'إدارة النسخ الاحتياطية',
                        subtitle: 'إنشاء واستعادة النسخ الاحتياطية',
                        icon: Icons.cloud_upload,
                        onTap: _openBackupManagement,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSettingsSection(
                    title: 'إعدادات المطور',
                    icon: Icons.developer_mode,
                    color: Colors.red,
                    children: [
                      _buildActionSetting(
                        title: 'إعادة تعيين قاعدة البيانات',
                        subtitle:
                            'حذف جميع البيانات وإعادة إنشاء قاعدة البيانات',
                        icon: Icons.refresh,
                        onTap: () =>
                            Navigator.pushNamed(context, '/developer-settings'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildSettingsSection(
                    title: 'حول التطبيق',
                    icon: Icons.info,
                    color: Colors.grey,
                    children: [
                      _buildInfoSetting(
                        title: 'إصدار التطبيق',
                        subtitle: '1.0.0',
                      ),
                      _buildInfoSetting(
                        title: 'تاريخ آخر تحديث',
                        subtitle: '2025-06-19',
                      ),
                      _buildActionSetting(
                        title: 'شروط الاستخدام',
                        subtitle: 'اقرأ شروط وأحكام الاستخدام',
                        icon: Icons.description,
                        onTap: _showTerms,
                      ),
                      _buildActionSetting(
                        title: 'سياسة الخصوصية',
                        subtitle: 'كيف نحمي بياناتك',
                        icon: Icons.privacy_tip,
                        onTap: _showPrivacyPolicy,
                      ),
                    ],
                  ),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'الإعدادات',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.primaryColor,
                AppTheme.primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 40),
                Icon(
                  Icons.settings,
                  size: 64,
                  color: Colors.white,
                ),
                SizedBox(height: 16),
                Text(
                  'إعدادات التطبيق',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.blue.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: const BoxDecoration(
                color: AppTheme.primaryColor,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مدير النظام',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'نظام إدارة المزارع والري',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                'نشط',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchSetting({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: Switch.adaptive(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildSliderSetting({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppTheme.primaryColor,
              inactiveTrackColor: AppTheme.primaryColor.withValues(alpha: 0.3),
              thumbColor: AppTheme.primaryColor,
              overlayColor: AppTheme.primaryColor.withValues(alpha: 0.2),
              valueIndicatorColor: AppTheme.primaryColor,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: divisions,
              label: value.toInt().toString(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownSetting({
    required String title,
    required String subtitle,
    required String value,
    required List<Map<String, String>> items,
    required ValueChanged<String?> onChanged,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        underline: const SizedBox.shrink(),
        items: items.map((item) {
          return DropdownMenuItem<String>(
            value: item['value'],
            child: Text(item['label']!),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildActionSetting({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildInfoSetting({
    required String title,
    required String subtitle,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      trailing: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // دوال الإجراءات
  void _createBackup() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء نسخة احتياطية'),
        content: const Text('هل تريد إنشاء نسخة احتياطية من جميع البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSnackBar('تم إنشاء النسخة الاحتياطية بنجاح');
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  void _restoreBackup() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة البيانات'),
        content: const Text(
            'هل تريد استعادة البيانات من نسخة احتياطية؟\n\nتحذير: سيتم استبدال البيانات الحالية.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showSnackBar('تم استعادة البيانات بنجاح');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    _showSnackBar('جاري تصدير البيانات...');
    // محاكاة عملية التصدير
    Future.delayed(const Duration(seconds: 2), () {
      _showSnackBar('تم تصدير البيانات بنجاح');
    });
  }

  void _contactSupport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تواصل معنا'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('يمكنك التواصل معنا عبر:'),
            SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.email, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('<EMAIL>'),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.phone, color: AppTheme.primaryColor),
                SizedBox(width: 8),
                Text('+967 776066142'),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showTerms() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('شروط الاستخدام'),
            backgroundColor: AppTheme.primaryColor,
          ),
          body: const Padding(
            padding: EdgeInsets.all(16),
            child: SingleChildScrollView(
              child: Text(
                '''شروط وأحكام استخدام تطبيق إدارة المزارع والري

1. قبول الشروط
باستخدام هذا التطبيق، فإنك توافق على الالتزام بهذه الشروط والأحكام.

2. استخدام التطبيق
- يُستخدم التطبيق لإدارة المزارع وعمليات الري فقط
- يجب استخدام التطبيق بطريقة قانونية ومسؤولة
- لا يُسمح بنسخ أو توزيع التطبيق بدون إذن

3. البيانات والخصوصية
- نحن نحترم خصوصية بياناتك
- البيانات المدخلة تُحفظ محلياً على جهازك
- لا نشارك بياناتك مع أطراف ثالثة

4. المسؤولية
- المستخدم مسؤول عن دقة البيانات المدخلة
- الشركة غير مسؤولة عن أي أضرار ناتجة عن سوء الاستخدام

5. التحديثات
- قد نقوم بتحديث التطبيق دورياً
- التحديثات قد تتضمن ميزات جديدة أو إصلاحات

6. إنهاء الخدمة
- يحق لك إلغاء تثبيت التطبيق في أي وقت
- نحتفظ بالحق في إيقاف الخدمة مع إشعار مسبق

للاستفسارات: <EMAIL>''',
                style: TextStyle(height: 1.6),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showPrivacyPolicy() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text('سياسة الخصوصية'),
            backgroundColor: AppTheme.primaryColor,
          ),
          body: const Padding(
            padding: EdgeInsets.all(16),
            child: SingleChildScrollView(
              child: Text(
                '''سياسة الخصوصية لتطبيق إدارة المزارع والري

نحن نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية.

1. البيانات التي نجمعها
- بيانات العملاء والمزارع التي تدخلها
- معلومات التسقيات والمدفوعات
- إعدادات التطبيق وتفضيلاتك

2. كيف نستخدم البيانات
- لتوفير خدمات إدارة المزارع
- لحفظ إعداداتك وتفضيلاتك
- لتحسين أداء التطبيق

3. تخزين البيانات
- جميع البيانات تُحفظ محلياً على جهازك
- لا نرسل بياناتك إلى خوادم خارجية
- أنت المسؤول الوحيد عن بياناتك

4. مشاركة البيانات
- لا نشارك بياناتك مع أي طرف ثالث
- لا نبيع أو نؤجر معلوماتك الشخصية
- بياناتك تبقى ملكك الخاص

5. أمان البيانات
- نستخدم تقنيات التشفير لحماية البيانات
- ننصح بعمل نسخ احتياطية دورية
- تأكد من حماية جهازك بكلمة مرور

6. حقوقك
- يمكنك حذف بياناتك في أي وقت
- يمكنك تصدير بياناتك
- يمكنك التحكم في إعدادات الخصوصية

7. تحديثات السياسة
- قد نحدث هذه السياسة من وقت لآخر
- سنخطرك بأي تغييرات مهمة

للاستفسارات حول الخصوصية: <EMAIL>''',
                style: TextStyle(height: 1.6),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// فتح صفحة إدارة النسخ الاحتياطية
  void _openBackupManagement() {
    // الحصول على البيانات من البلوكات
    final clientState = context.read<ClientBloc>().state;
    final farmState = context.read<FarmBloc>().state;
    final irrigationState = context.read<IrrigationBloc>().state;
    final paymentState = context.read<PaymentBloc>().state;

    if (clientState is ClientsLoaded &&
        farmState is FarmsLoaded &&
        irrigationState is IrrigationsLoaded &&
        paymentState is PaymentsLoaded) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BackupManagementPage(
            clients: clientState.clients,
            farms: farmState.farms,
            irrigations: irrigationState.irrigations,
            payments: paymentState.payments,
            onDataRestored: (restoredData) {
              // يمكن إضافة منطق لتحديث البلوكات هنا
              _showSnackBar(
                  'تم استعادة البيانات بنجاح! يرجى إعادة تشغيل التطبيق.');
            },
          ),
        ),
      );
    } else {
      _showSnackBar('يرجى انتظار تحميل البيانات أولاً');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.primaryColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة مركز المساعدة المحسن
class EnhancedHelpCenterPage extends StatefulWidget {
  const EnhancedHelpCenterPage({super.key});

  @override
  State<EnhancedHelpCenterPage> createState() => _EnhancedHelpCenterPageState();
}

class _EnhancedHelpCenterPageState extends State<EnhancedHelpCenterPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'مركز المساعدة',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(icon: Icon(Icons.rocket_launch), text: 'بدء سريع'),
            Tab(icon: Icon(Icons.book), text: 'الدليل الشامل'),
            Tab(icon: Icon(Icons.question_answer), text: 'الأسئلة الشائعة'),
            Tab(icon: Icon(Icons.video_library), text: 'الفيديو'),
            Tab(icon: Icon(Icons.contact_support), text: 'الدعم'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildQuickStartTab(),
                _buildComprehensiveGuideTab(),
                _buildFAQTab(),
                _buildVideoTutorialsTab(),
                _buildSupportTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// شريط البحث
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'ابحث في المساعدة...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
        ),
      ),
    );
  }

  /// تبويب البدء السريع
  Widget _buildQuickStartTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
              'مرحباً بك في تطبيق إدارة التسقيات', Icons.waving_hand),
          const SizedBox(height: 16),
          _buildQuickStartCard(
            'إضافة عميل جديد',
            'تعلم كيفية إضافة عميل جديد إلى النظام',
            Icons.person_add,
            Colors.blue,
            () => _showQuickStartGuide('add_client'),
          ),
          _buildQuickStartCard(
            'إضافة مزرعة',
            'تعلم كيفية إضافة مزرعة جديدة',
            Icons.landscape,
            Colors.green,
            () => _showQuickStartGuide('add_farm'),
          ),
          _buildQuickStartCard(
            'تسجيل تسقية',
            'تعلم كيفية تسجيل عملية تسقية جديدة',
            Icons.water_drop,
            Colors.cyan,
            () => _showQuickStartGuide('add_irrigation'),
          ),
          _buildQuickStartCard(
            'تسجيل دفعة',
            'تعلم كيفية تسجيل دفعة جديدة',
            Icons.payment,
            Colors.orange,
            () => _showQuickStartGuide('add_payment'),
          ),
          _buildQuickStartCard(
            'إنشاء تقرير',
            'تعلم كيفية إنشاء وتصدير التقارير',
            Icons.assessment,
            Colors.purple,
            () => _showQuickStartGuide('create_report'),
          ),
          const SizedBox(height: 24),
          _buildSectionHeader('نصائح سريعة', Icons.lightbulb),
          const SizedBox(height: 16),
          _buildTipCard(
            'استخدم البحث للعثور على العملاء والمزارع بسرعة',
            Icons.search,
          ),
          _buildTipCard(
            'احتفظ بنسخة احتياطية من بياناتك بانتظام',
            Icons.backup,
          ),
          _buildTipCard(
            'راجع التقارير الشهرية لمراقبة الأداء',
            Icons.trending_up,
          ),
        ],
      ),
    );
  }

  /// تبويب الدليل الشامل
  Widget _buildComprehensiveGuideTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('الدليل الشامل للتطبيق', Icons.book),
          const SizedBox(height: 16),
          _buildGuideSection(
            'إدارة العملاء',
            [
              'إضافة عميل جديد',
              'تعديل بيانات العميل',
              'حذف عميل',
              'عرض تفاصيل العميل',
              'إدارة حسابات العملاء',
            ],
            Icons.people,
            Colors.blue,
          ),
          _buildGuideSection(
            'إدارة المزارع',
            [
              'إضافة مزرعة جديدة',
              'تعديل بيانات المزرعة',
              'حذف مزرعة',
              'عرض تفاصيل المزرعة',
              'إدارة مساحات المزارع',
            ],
            Icons.landscape,
            Colors.green,
          ),
          _buildGuideSection(
            'إدارة التسقيات',
            [
              'تسجيل تسقية جديدة',
              'تعديل بيانات التسقية',
              'حذف تسقية',
              'عرض تفاصيل التسقية',
              'حساب تكاليف التسقية',
            ],
            Icons.water_drop,
            Colors.cyan,
          ),
          _buildGuideSection(
            'إدارة المدفوعات',
            [
              'تسجيل دفعة جديدة',
              'تعديل بيانات الدفعة',
              'حذف دفعة',
              'عرض تفاصيل الدفعة',
              'إدارة أرصدة العملاء',
            ],
            Icons.payment,
            Colors.orange,
          ),
          _buildGuideSection(
            'إدارة الصناديق',
            [
              'إضافة صندوق جديد',
              'تعديل بيانات الصندوق',
              'حذف صندوق',
              'عرض تفاصيل الصندوق',
              'إدارة أرصدة الصناديق',
            ],
            Icons.account_balance,
            Colors.purple,
          ),
          _buildGuideSection(
            'التقارير والإحصائيات',
            [
              'إنشاء تقرير شامل',
              'تصدير التقارير',
              'عرض الإحصائيات',
              'تحليل البيانات',
              'مقارنة الفترات',
            ],
            Icons.assessment,
            Colors.indigo,
          ),
        ],
      ),
    );
  }

  /// تبويب الأسئلة الشائعة
  Widget _buildFAQTab() {
    final faqs = [
      {
        'question': 'كيف يمكنني إضافة عميل جديد؟',
        'answer':
            'اذهب إلى صفحة العملاء واضغط على زر "+" لإضافة عميل جديد، ثم املأ البيانات المطلوبة.',
      },
      {
        'question': 'كيف يمكنني تسجيل تسقية جديدة؟',
        'answer':
            'اذهب إلى صفحة التسقيات واضغط على زر "+" لإضافة تسقية جديدة، ثم اختر المزرعة واملأ التفاصيل.',
      },
      {
        'question': 'كيف يمكنني تصدير التقارير؟',
        'answer':
            'اذهب إلى صفحة التقارير واختر التقرير المطلوب، ثم اضغط على زر التصدير واختر الصيغة المطلوبة.',
      },
      {
        'question': 'كيف يمكنني إنشاء نسخة احتياطية؟',
        'answer':
            'اذهب إلى الإعدادات ثم اختر "إنشاء نسخة احتياطية" واختر مكان الحفظ.',
      },
      {
        'question': 'كيف يمكنني تغيير الوضع المظلم؟',
        'answer':
            'اذهب إلى الإعدادات ثم اختر "المظهر والثيم" واختر الوضع المطلوب.',
      },
      {
        'question': 'كيف يمكنني البحث في البيانات؟',
        'answer':
            'استخدم شريط البحث في أعلى الصفحات للبحث في العملاء والمزارع والتسقيات.',
      },
      {
        'question': 'كيف يمكنني إدارة أرصدة العملاء؟',
        'answer':
            'اذهب إلى صفحة حسابات العملاء لعرض وإدارة أرصدة جميع العملاء.',
      },
      {
        'question': 'كيف يمكنني إضافة مزرعة جديدة؟',
        'answer':
            'اذهب إلى صفحة المزارع واضغط على زر "+" لإضافة مزرعة جديدة، ثم املأ البيانات المطلوبة.',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: faqs.length,
      itemBuilder: (context, index) {
        final faq = faqs[index];
        return _buildFAQCard(faq['question']!, faq['answer']!);
      },
    );
  }

  /// تبويب الفيديو التعليمي
  Widget _buildVideoTutorialsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('الفيديو التعليمي', Icons.video_library),
          const SizedBox(height: 16),
          _buildVideoCard(
            'مقدمة للتطبيق',
            'تعرف على الميزات الأساسية للتطبيق',
            '5:30',
            Icons.play_circle,
            () => _showVideoTutorial('intro'),
          ),
          _buildVideoCard(
            'إضافة عميل جديد',
            'خطوات إضافة عميل جديد إلى النظام',
            '3:45',
            Icons.person_add,
            () => _showVideoTutorial('add_client'),
          ),
          _buildVideoCard(
            'إدارة المزارع',
            'كيفية إضافة وإدارة المزارع',
            '4:20',
            Icons.landscape,
            () => _showVideoTutorial('manage_farms'),
          ),
          _buildVideoCard(
            'تسجيل التسقيات',
            'كيفية تسجيل عمليات التسقية',
            '6:15',
            Icons.water_drop,
            () => _showVideoTutorial('record_irrigation'),
          ),
          _buildVideoCard(
            'إنشاء التقارير',
            'كيفية إنشاء وتصدير التقارير',
            '7:30',
            Icons.assessment,
            () => _showVideoTutorial('create_reports'),
          ),
          _buildVideoCard(
            'النسخ الاحتياطي',
            'كيفية إنشاء واستعادة النسخ الاحتياطية',
            '4:50',
            Icons.backup,
            () => _showVideoTutorial('backup_restore'),
          ),
        ],
      ),
    );
  }

  /// تبويب الدعم
  Widget _buildSupportTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('الدعم الفني', Icons.contact_support),
          const SizedBox(height: 16),
          _buildSupportCard(
            'البريد الإلكتروني',
            'راسلنا عبر البريد الإلكتروني',
            Icons.email,
            Colors.blue,
            () => _contactSupport('email'),
          ),
          _buildSupportCard(
            'الهاتف',
            'اتصل بنا مباشرة',
            Icons.phone,
            Colors.green,
            () => _contactSupport('phone'),
          ),
          _buildSupportCard(
            'الواتساب',
            'راسلنا عبر الواتساب',
            Icons.whatsapp,
            Colors.green,
            () => _contactSupport('whatsapp'),
          ),
          _buildSupportCard(
            'التلجرام',
            'راسلنا عبر التلجرام',
            Icons.telegram,
            Colors.blue,
            () => _contactSupport('telegram'),
          ),
          const SizedBox(height: 24),
          _buildSectionHeader('معلومات التطبيق', Icons.info),
          const SizedBox(height: 16),
          _buildInfoCard('إصدار التطبيق', '1.0.0'),
          _buildInfoCard('تاريخ الإصدار', '2024'),
          _buildInfoCard('المطور', 'فريق التطوير'),
          _buildInfoCard('الموقع الإلكتروني', 'www.example.com'),
        ],
      ),
    );
  }

  // دوال مساعدة لبناء العناصر

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  Widget _buildQuickStartCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                          ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTipCard(String tip, IconData icon) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(icon, color: Colors.orange, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                tip,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuideSection(
    String title,
    List<String> items,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        children: items
            .map((item) => ListTile(
                  leading: const Icon(Icons.check_circle,
                      color: Colors.green, size: 20),
                  title: Text(item),
                  onTap: () => _showGuideDetail(title, item),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildFAQCard(String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        title: Text(
          question,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              answer,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoCard(
    String title,
    String description,
    String duration,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: Colors.red, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                          ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  duration,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSupportCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                          ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, String value) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  // دوال التفاعل

  void _showQuickStartGuide(String guideType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getGuideTitle(guideType)),
        content: Text(_getGuideContent(guideType)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showGuideDetail(String section, String item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(item),
        content: Text('تفاصيل $item في قسم $section'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showVideoTutorial(String tutorial) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getVideoTitle(tutorial)),
        content:
            Text('سيتم تشغيل الفيديو التعليمي: ${_getVideoTitle(tutorial)}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // هنا يمكن إضافة منطق تشغيل الفيديو
            },
            child: const Text('تشغيل'),
          ),
        ],
      ),
    );
  }

  void _contactSupport(String method) {
    String message = '';
    switch (method) {
      case 'email':
        message = 'راسلنا على: <EMAIL>';
        break;
      case 'phone':
        message = 'اتصل بنا على: +966-50-123-4567';
        break;
      case 'whatsapp':
        message = 'راسلنا على الواتساب: +966-50-123-4567';
        break;
      case 'telegram':
        message = 'راسلنا على التلجرام: @support_channel';
        break;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التواصل'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  String _getGuideTitle(String guideType) {
    switch (guideType) {
      case 'add_client':
        return 'إضافة عميل جديد';
      case 'add_farm':
        return 'إضافة مزرعة';
      case 'add_irrigation':
        return 'تسجيل تسقية';
      case 'add_payment':
        return 'تسجيل دفعة';
      case 'create_report':
        return 'إنشاء تقرير';
      default:
        return 'الدليل';
    }
  }

  String _getGuideContent(String guideType) {
    switch (guideType) {
      case 'add_client':
        return '1. اذهب إلى صفحة العملاء\n2. اضغط على زر "+"\n3. املأ البيانات المطلوبة\n4. اضغط على "حفظ"';
      case 'add_farm':
        return '1. اذهب إلى صفحة المزارع\n2. اضغط على زر "+"\n3. املأ بيانات المزرعة\n4. اضغط على "حفظ"';
      case 'add_irrigation':
        return '1. اذهب إلى صفحة التسقيات\n2. اضغط على زر "+"\n3. اختر المزرعة\n4. املأ تفاصيل التسقية\n5. اضغط على "حفظ"';
      case 'add_payment':
        return '1. اذهب إلى صفحة المدفوعات\n2. اضغط على زر "+"\n3. اختر العميل\n4. املأ تفاصيل الدفعة\n5. اضغط على "حفظ"';
      case 'create_report':
        return '1. اذهب إلى صفحة التقارير\n2. اختر نوع التقرير\n3. حدد الفترة الزمنية\n4. اضغط على "إنشاء"\n5. اضغط على "تصدير"';
      default:
        return 'دليل مفصل للميزة المطلوبة';
    }
  }

  String _getVideoTitle(String tutorial) {
    switch (tutorial) {
      case 'intro':
        return 'مقدمة للتطبيق';
      case 'add_client':
        return 'إضافة عميل جديد';
      case 'manage_farms':
        return 'إدارة المزارع';
      case 'record_irrigation':
        return 'تسجيل التسقيات';
      case 'create_reports':
        return 'إنشاء التقارير';
      case 'backup_restore':
        return 'النسخ الاحتياطي';
      default:
        return 'فيديو تعليمي';
    }
  }
}

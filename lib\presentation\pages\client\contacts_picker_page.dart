import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../blocs/contacts/contacts_bloc.dart';
import '../../blocs/contacts/contacts_event.dart';
import '../../blocs/contacts/contacts_state.dart';
import '../../viewmodels/contact_view_model.dart';

class ContactsPickerPage extends StatefulWidget {
  final void Function(ContactViewModel contact) onContactSelected;
  const ContactsPickerPage({super.key, required this.onContactSelected});

  @override
  State<ContactsPickerPage> createState() => _ContactsPickerPageState();
}

class _ContactsPickerPageState extends State<ContactsPickerPage> {
  @override
  void initState() {
    super.initState();
    context.read<ContactsBloc>().add(LoadContacts());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختر جهة اتصال'),
        backgroundColor: Colors.blue,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                context.read<ContactsBloc>().add(CheckPermission()),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'بحث بالاسم أو الرقم',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                context.read<ContactsBloc>().add(SearchContacts(value));
              },
            ),
          ),
          Expanded(
            child: BlocBuilder<ContactsBloc, ContactsState>(
              builder: (context, state) {
                if (state is PermissionDenied) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text(
                          'تم رفض إذن الوصول إلى جهات الاتصال. يرجى السماح بالإذن.',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 20),
                        ElevatedButton(
                          onPressed: () async {
                            final status = await Permission.contacts.request();
                            if (status.isGranted) {
                              context.read<ContactsBloc>().add(LoadContacts());
                            }
                          },
                          child: const Text('إعادة المحاولة'),
                        ),
                        const SizedBox(height: 10),
                        ElevatedButton(
                          onPressed: () => openAppSettings(),
                          child: const Text('فتح الإعدادات'),
                        ),
                      ],
                    ),
                  );
                } else if (state is ContactsLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is ContactsError) {
                  return Center(
                      child: Text(state.message,
                          style: const TextStyle(color: Colors.red)));
                } else if (state is ContactsLoaded) {
                  if (state.contacts.isEmpty) {
                    return const Center(
                        child: Text('لا توجد جهات اتصال متاحة'));
                  }
                  return ListView.separated(
                    itemCount: state.contacts.length,
                    separatorBuilder: (_, __) => const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final contact = state.contacts[index];
                      return ListTile(
                        leading: const Icon(Icons.person),
                        title: Text(contact.name),
                        subtitle: Text(contact.phone),
                        trailing: ElevatedButton(
                          onPressed: () {
                            widget.onContactSelected(contact);
                            Navigator.pop(context);
                          },
                          child: const Text('اختيار'),
                        ),
                      );
                    },
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }
}

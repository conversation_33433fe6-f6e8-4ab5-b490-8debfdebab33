import 'package:flutter/material.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/services/simple_notification_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// صفحة إعدادات الإشعارات المتقدمة
class AdvancedNotificationsSettingsPage extends StatefulWidget {
  const AdvancedNotificationsSettingsPage({super.key});

  @override
  State<AdvancedNotificationsSettingsPage> createState() =>
      _AdvancedNotificationsSettingsPageState();
}

class _AdvancedNotificationsSettingsPageState
    extends State<AdvancedNotificationsSettingsPage> {
  bool _isLoading = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _ledEnabled = false;
  bool _quietHoursEnabled = false;
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 7, minute: 0);
  String _soundType = 'default';

  // فئات الإشعارات
  bool _infoEnabled = true;
  bool _warningEnabled = true;
  bool _errorEnabled = true;
  bool _successEnabled = true;
  bool _reminderEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      setState(() {
        _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
        _soundEnabled = prefs.getBool('notifications_sound') ?? true;
        _vibrationEnabled = prefs.getBool('notifications_vibration') ?? true;
        _ledEnabled = prefs.getBool('notifications_led') ?? false;
        _quietHoursEnabled =
            prefs.getBool('notifications_quiet_hours') ?? false;
        _quietHoursStart = TimeOfDay(
          hour: prefs.getInt('quiet_hours_start_hour') ?? 22,
          minute: prefs.getInt('quiet_hours_start_minute') ?? 0,
        );
        _quietHoursEnd = TimeOfDay(
          hour: prefs.getInt('quiet_hours_end_hour') ?? 7,
          minute: prefs.getInt('quiet_hours_end_minute') ?? 0,
        );
        _soundType = prefs.getString('notifications_sound_type') ?? 'default';

        // فئات الإشعارات
        final enabledCategories =
            prefs.getStringList('notifications_enabled_categories') ??
                [
                  'NotificationType.info',
                  'NotificationType.warning',
                  'NotificationType.error',
                  'NotificationType.success',
                  'NotificationType.reminder'
                ];

        _infoEnabled = enabledCategories.contains('NotificationType.info');
        _warningEnabled =
            enabledCategories.contains('NotificationType.warning');
        _errorEnabled = enabledCategories.contains('NotificationType.error');
        _successEnabled =
            enabledCategories.contains('NotificationType.success');
        _reminderEnabled =
            enabledCategories.contains('NotificationType.reminder');
      });
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الإشعارات: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    setState(() => _isLoading = true);

    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool('notifications_sound', _soundEnabled);
      await prefs.setBool('notifications_vibration', _vibrationEnabled);
      await prefs.setBool('notifications_led', _ledEnabled);
      await prefs.setBool('notifications_quiet_hours', _quietHoursEnabled);
      await prefs.setInt('quiet_hours_start_hour', _quietHoursStart.hour);
      await prefs.setInt('quiet_hours_start_minute', _quietHoursStart.minute);
      await prefs.setInt('quiet_hours_end_hour', _quietHoursEnd.hour);
      await prefs.setInt('quiet_hours_end_minute', _quietHoursEnd.minute);
      await prefs.setString('notifications_sound_type', _soundType);

      // حفظ فئات الإشعارات
      final enabledCategories = <String>[];
      if (_infoEnabled) enabledCategories.add('NotificationType.info');
      if (_warningEnabled) enabledCategories.add('NotificationType.warning');
      if (_errorEnabled) enabledCategories.add('NotificationType.error');
      if (_successEnabled) enabledCategories.add('NotificationType.success');
      if (_reminderEnabled) enabledCategories.add('NotificationType.reminder');

      await prefs.setStringList(
          'notifications_enabled_categories', enabledCategories);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات المتقدمة'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _testNotification,
            icon: const Icon(Icons.notifications_active),
            tooltip: 'اختبار الإشعارات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildGeneralSettings(),
                  const SizedBox(height: 24),
                  _buildSoundSettings(),
                  const SizedBox(height: 24),
                  _buildVibrationSettings(),
                  const SizedBox(height: 24),
                  _buildQuietHoursSettings(),
                  const SizedBox(height: 24),
                  _buildCategorySettings(),
                  const SizedBox(height: 24),
                  _buildAdvancedSettings(),
                  const SizedBox(height: 24),
                  _buildTestNotificationsSection(),
                  const SizedBox(height: 32),
                  _buildSaveButton(),
                ],
              ),
            ),
    );
  }

  /// إعدادات عامة
  Widget _buildGeneralSettings() {
    return _buildSettingsCard(
      title: 'الإعدادات العامة',
      icon: Icons.settings,
      children: [
        SwitchListTile(
          title: const Text('تفعيل الإشعارات'),
          subtitle: const Text('تفعيل أو إلغاء جميع الإشعارات'),
          value: _notificationsEnabled,
          onChanged: (value) {
            setState(() {
              _notificationsEnabled = value;
            });
          },
          secondary: Icon(
            _notificationsEnabled
                ? Icons.notifications_active
                : Icons.notifications_off,
            color: _notificationsEnabled ? Colors.green : Colors.grey,
          ),
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.info_outline, color: AppTheme.primaryColor),
          title: const Text('حالة الإشعارات'),
          subtitle: Text(
            _notificationsEnabled ? 'مفعلة' : 'معطلة',
            style: TextStyle(
              color: _notificationsEnabled ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// إعدادات الصوت
  Widget _buildSoundSettings() {
    return _buildSettingsCard(
      title: 'إعدادات الصوت',
      icon: Icons.volume_up,
      children: [
        SwitchListTile(
          title: const Text('تفعيل الصوت'),
          subtitle: const Text('تشغيل صوت عند استلام الإشعارات'),
          value: _soundEnabled,
          onChanged: (value) {
            setState(() {
              _soundEnabled = value;
            });
          },
          secondary: Icon(
            _soundEnabled ? Icons.volume_up : Icons.volume_off,
            color: _soundEnabled ? Colors.blue : Colors.grey,
          ),
        ),
        if (_soundEnabled) ...[
          const Divider(),
          ListTile(
            leading: const Icon(Icons.music_note, color: AppTheme.primaryColor),
            title: const Text('نوع الصوت'),
            subtitle: Text(_getSoundTypeDisplayName(_soundType)),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: _showSoundTypeDialog,
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.play_circle_outline,
                color: AppTheme.primaryColor),
            title: const Text('اختبار الصوت'),
            subtitle: const Text('تشغيل صوت تجريبي'),
            onTap: _testSound,
          ),
        ],
      ],
    );
  }

  /// إعدادات الاهتزاز
  Widget _buildVibrationSettings() {
    return _buildSettingsCard(
      title: 'إعدادات الاهتزاز',
      icon: Icons.vibration,
      children: [
        SwitchListTile(
          title: const Text('تفعيل الاهتزاز'),
          subtitle: const Text('اهتزاز الجهاز عند استلام الإشعارات'),
          value: _vibrationEnabled,
          onChanged: (value) {
            setState(() {
              _vibrationEnabled = value;
            });
          },
          secondary: Icon(
            _vibrationEnabled ? Icons.vibration : Icons.vibration_outlined,
            color: _vibrationEnabled ? Colors.orange : Colors.grey,
          ),
        ),
        if (_vibrationEnabled) ...[
          const Divider(),
          ListTile(
            leading: const Icon(Icons.tune, color: AppTheme.primaryColor),
            title: const Text('نمط الاهتزاز'),
            subtitle: const Text('نمط اهتزاز قياسي'),
            trailing: const Icon(Icons.check, color: Colors.green),
          ),
        ],
      ],
    );
  }

  /// إعدادات ساعات الهدوء
  Widget _buildQuietHoursSettings() {
    return _buildSettingsCard(
      title: 'ساعات الهدوء',
      icon: Icons.bedtime,
      children: [
        SwitchListTile(
          title: const Text('تفعيل ساعات الهدوء'),
          subtitle: const Text('إيقاف الإشعارات خلال ساعات محددة'),
          value: _quietHoursEnabled,
          onChanged: (value) {
            setState(() {
              _quietHoursEnabled = value;
            });
          },
          secondary: Icon(
            _quietHoursEnabled ? Icons.bedtime : Icons.bedtime_outlined,
            color: _quietHoursEnabled ? Colors.purple : Colors.grey,
          ),
        ),
        if (_quietHoursEnabled) ...[
          const Divider(),
          ListTile(
            leading:
                const Icon(Icons.access_time, color: AppTheme.primaryColor),
            title: const Text('وقت البداية'),
            subtitle: Text(_quietHoursStart.format(context)),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _selectTime(true),
          ),
          const Divider(),
          ListTile(
            leading:
                const Icon(Icons.access_time, color: AppTheme.primaryColor),
            title: const Text('وقت النهاية'),
            subtitle: Text(_quietHoursEnd.format(context)),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _selectTime(false),
          ),
          const Divider(),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.purple, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'الإشعارات ستكون صامتة من ${_quietHoursStart.format(context)} إلى ${_quietHoursEnd.format(context)}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.purple,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// إعدادات الفئات
  Widget _buildCategorySettings() {
    return _buildSettingsCard(
      title: 'فئات الإشعارات',
      icon: Icons.category,
      children: [
        const ListTile(
          leading: Icon(Icons.info_outline, color: AppTheme.primaryColor),
          title: Text('اختر أنواع الإشعارات المطلوبة'),
          subtitle: Text('يمكنك تفعيل أو إلغاء أنواع معينة من الإشعارات'),
        ),
        const Divider(),
        CheckboxListTile(
          title: const Text('المعلومات'),
          subtitle: const Text('إشعارات المعلومات العامة'),
          value: _infoEnabled,
          onChanged: (value) {
            setState(() {
              _infoEnabled = value ?? true;
            });
          },
          secondary: const Icon(Icons.info, color: Colors.grey),
        ),
        CheckboxListTile(
          title: const Text('التحذيرات'),
          subtitle: const Text('تنبيهات التحذير المهمة'),
          value: _warningEnabled,
          onChanged: (value) {
            setState(() {
              _warningEnabled = value ?? true;
            });
          },
          secondary: const Icon(Icons.warning, color: Colors.orange),
        ),
        CheckboxListTile(
          title: const Text('الأخطاء'),
          subtitle: const Text('إشعارات الأخطاء والمشاكل'),
          value: _errorEnabled,
          onChanged: (value) {
            setState(() {
              _errorEnabled = value ?? true;
            });
          },
          secondary: const Icon(Icons.error, color: Colors.red),
        ),
        CheckboxListTile(
          title: const Text('النجاح'),
          subtitle: const Text('إشعارات النجاح والإنجازات'),
          value: _successEnabled,
          onChanged: (value) {
            setState(() {
              _successEnabled = value ?? true;
            });
          },
          secondary: const Icon(Icons.check_circle, color: Colors.green),
        ),
        CheckboxListTile(
          title: const Text('التذكيرات'),
          subtitle: const Text('التذكيرات والمواعيد'),
          value: _reminderEnabled,
          onChanged: (value) {
            setState(() {
              _reminderEnabled = value ?? true;
            });
          },
          secondary: const Icon(Icons.schedule, color: Colors.blue),
        ),
      ],
    );
  }

  /// إعدادات متقدمة
  Widget _buildAdvancedSettings() {
    return _buildSettingsCard(
      title: 'إعدادات متقدمة',
      icon: Icons.tune,
      children: [
        SwitchListTile(
          title: const Text('تفعيل إضاءة LED'),
          subtitle: const Text('إضاءة LED عند استلام الإشعارات'),
          value: _ledEnabled,
          onChanged: (value) {
            setState(() {
              _ledEnabled = value;
            });
          },
          secondary: Icon(
            _ledEnabled ? Icons.lightbulb : Icons.lightbulb_outline,
            color: _ledEnabled ? Colors.yellow : Colors.grey,
          ),
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.clear_all, color: AppTheme.primaryColor),
          title: const Text('مسح جميع الإشعارات'),
          subtitle: const Text('إلغاء جميع الإشعارات الحالية'),
          onTap: _clearAllNotifications,
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.restore, color: AppTheme.primaryColor),
          title: const Text('إعادة تعيين الإعدادات'),
          subtitle: const Text('العودة إلى الإعدادات الافتراضية'),
          onTap: _resetToDefaults,
        ),
      ],
    );
  }

  /// قسم اختبار الإشعارات
  Widget _buildTestNotificationsSection() {
    return _buildSettingsCard(
      title: 'اختبار الإشعارات',
      icon: Icons.notifications_active,
      children: [
        const ListTile(
          leading: Icon(Icons.info_outline, color: AppTheme.primaryColor),
          title: Text('اختبار أنواع مختلفة من الإشعارات'),
          subtitle: Text('اضغط على أي نوع لاختباره'),
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.info, color: Colors.grey),
          title: const Text('اختبار معلومات'),
          subtitle: const Text('إشعار معلومات تجريبي'),
          onTap: () => _testSpecificNotification('info'),
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.error_outline, color: Colors.red),
          title: const Text('اختبار خطأ'),
          subtitle: const Text('إشعار خطأ تجريبي'),
          onTap: () => _testSpecificNotification('error'),
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.warning_amber, color: Colors.orange),
          title: const Text('اختبار تحذير'),
          subtitle: const Text('إشعار تحذير تجريبي'),
          onTap: () => _testSpecificNotification('warning'),
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.schedule, color: Colors.blue),
          title: const Text('اختبار تذكير'),
          subtitle: const Text('إشعار تذكير تجريبي'),
          onTap: () => _testSpecificNotification('reminder'),
        ),
        const Divider(),
        ListTile(
          leading: const Icon(Icons.check_circle_outline, color: Colors.green),
          title: const Text('اختبار نجاح'),
          subtitle: const Text('إشعار نجاح تجريبي'),
          onTap: () => _testSpecificNotification('success'),
        ),
      ],
    );
  }

  /// زر الحفظ
  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _saveSettings,
        icon: const Icon(Icons.save, color: Colors.white),
        label: const Text(
          'حفظ الإعدادات',
          style: TextStyle(
              color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة الإعدادات
  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// عرض حوار نوع الصوت
  void _showSoundTypeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر نوع الصوت'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('افتراضي'),
              value: 'default',
              groupValue: _soundType,
              onChanged: (value) {
                setState(() {
                  _soundType = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('نغمة'),
              value: 'chime',
              groupValue: _soundType,
              onChanged: (value) {
                setState(() {
                  _soundType = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('جرس'),
              value: 'bell',
              groupValue: _soundType,
              onChanged: (value) {
                setState(() {
                  _soundType = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('تنبيه'),
              value: 'notification',
              groupValue: _soundType,
              onChanged: (value) {
                setState(() {
                  _soundType = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// اختيار الوقت
  Future<void> _selectTime(bool isStart) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStart ? _quietHoursStart : _quietHoursEnd,
    );
    if (picked != null) {
      setState(() {
        if (isStart) {
          _quietHoursStart = picked;
        } else {
          _quietHoursEnd = picked;
        }
      });
    }
  }

  /// اختبار الصوت
  void _testSound() {
    // في الخدمة المبسطة، الصوت يعمل تلقائياً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم اختبار الصوت مع الإشعار التالي'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// مسح جميع الإشعارات
  void _clearAllNotifications() {
    // في الخدمة المبسطة، لا توجد إشعارات مجدولة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('لا توجد إشعارات مجدولة للمسح'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// إعادة تعيين الإعدادات
  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text(
            'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _notificationsEnabled = true;
                _soundEnabled = true;
                _vibrationEnabled = true;
                _ledEnabled = false;
                _quietHoursEnabled = false;
                _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
                _quietHoursEnd = const TimeOfDay(hour: 7, minute: 0);
                _soundType = 'default';
                _infoEnabled = true;
                _warningEnabled = true;
                _errorEnabled = true;
                _successEnabled = true;
                _reminderEnabled = true;
              });
              Navigator.pop(context);
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  /// اختبار الإشعار عام
  Future<void> _testNotification() async {
    try {
      await SimpleNotificationService.showNotification(
        title: 'اختبار الإعدادات المتقدمة',
        body: 'هذا إشعار تجريبي لاختبار الإعدادات المتقدمة',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال إشعار الاختبار'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// اختبار إشعار محدد
  Future<void> _testSpecificNotification(String type) async {
    try {
      String title, body;
      switch (type) {
        case 'error':
          title = '❌ خطأ تجريبي';
          body = 'هذا إشعار خطأ تجريبي لاختبار النظام';
          break;
        case 'warning':
          title = '⚠️ تحذير تجريبي';
          body = 'هذا إشعار تحذير تجريبي لاختبار النظام';
          break;
        case 'reminder':
          title = '⏰ تذكير تجريبي';
          body = 'هذا إشعار تذكير تجريبي لاختبار النظام';
          break;
        case 'success':
          title = '✅ نجاح تجريبي';
          body = 'هذا إشعار نجاح تجريبي لاختبار النظام';
          break;
        case 'info':
        default:
          title = 'ℹ️ معلومات تجريبية';
          body = 'هذا إشعار معلومات تجريبي لاختبار النظام';
          break;
      }

      await SimpleNotificationService.showNotification(
        title: title,
        body: body,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إرسال إشعار $title'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// الحصول على اسم نوع الصوت
  String _getSoundTypeDisplayName(String soundType) {
    switch (soundType) {
      case 'default':
        return 'افتراضي';
      case 'chime':
        return 'نغمة';
      case 'bell':
        return 'جرس';
      case 'notification':
        return 'تنبيه';
      case 'custom':
        return 'مخصص';
      default:
        return 'افتراضي';
    }
  }
}

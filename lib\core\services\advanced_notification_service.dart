import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:vibration/vibration.dart';
import 'package:just_audio/just_audio.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة الإشعارات المحلية المتقدمة
class AdvancedNotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static NotificationSettings _settings = NotificationSettings();
  static final Map<String, int> _scheduledNotifications = {};

  static Future<void> initialize() async {
    try {
      tz.initializeTimeZones();
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const DarwinInitializationSettings iosSettings =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );
      const InitializationSettings settings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );
      await _notifications.initialize(
        settings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );
      await _loadSettings();
      await _setupNotificationChannels();
      debugPrint('✅ تم تهيئة خدمة الإشعارات المتقدمة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  static Future<void> _setupNotificationChannels() async {
    if (Platform.isAndroid) {
      // قناة الإشعارات العادية
      const AndroidNotificationChannel normalChannel =
          AndroidNotificationChannel(
        'normal_channel',
        'التنبيهات العادية',
        description: 'تنبيهات عامة ومعلومات',
        importance: Importance.high,
        playSound: true,
        enableVibration: true,
        enableLights: true,
        showBadge: true,
      );

      // قناة الإشعارات المهمة (مثل WhatsApp)
      const AndroidNotificationChannel importantChannel =
          AndroidNotificationChannel(
        'important_channel',
        'التنبيهات المهمة',
        description: 'تنبيهات مهمة وتذكيرات عاجلة',
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
        enableLights: true,
        showBadge: true,
        sound: RawResourceAndroidNotificationSound('notification_sound'),
      );

      // قناة الإشعارات الصامتة
      const AndroidNotificationChannel silentChannel =
          AndroidNotificationChannel(
        'silent_channel',
        'التنبيهات الصامتة',
        description: 'تنبيهات بدون صوت',
        importance: Importance.low,
        playSound: false,
        enableVibration: false,
        enableLights: false,
        showBadge: true,
      );

      // قناة إشعارات النظام (مثل SMS)
      const AndroidNotificationChannel systemChannel =
          AndroidNotificationChannel(
        'system_channel',
        'إشعارات النظام',
        description: 'إشعارات النظام المهمة',
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
        enableLights: true,
        showBadge: true,
        sound: RawResourceAndroidNotificationSound('notification_sound'),
      );

      // إنشاء القنوات
      final androidImplementation =
          _notifications.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      await androidImplementation?.createNotificationChannel(normalChannel);
      await androidImplementation?.createNotificationChannel(importantChannel);
      await androidImplementation?.createNotificationChannel(silentChannel);
      await androidImplementation?.createNotificationChannel(systemChannel);

      debugPrint('✅ تم إنشاء قنوات الإشعارات المتقدمة');
    }
  }

  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    NotificationType type = NotificationType.info,
    bool isImportant = false,
    List<NotificationAction>? actions,
  }) async {
    if (!_settings.enabled) return;
    if (_isInQuietHours()) {
      debugPrint('🔇 إشعار متجاهل بسبب ساعات الهدوء');
      return;
    }
    try {
      final channelId = _getChannelId(type, isImportant);

      // إعدادات Android متقدمة لإشعارات النظام
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        channelId,
        _getChannelName(type),
        channelDescription: _getChannelDescription(type),
        importance: isImportant ? Importance.max : Importance.high,
        priority: isImportant ? Priority.high : Priority.low,
        showWhen: true,
        when: DateTime.now().millisecondsSinceEpoch,
        enableLights: _settings.enableLED,
        enableVibration: _settings.enableVibration,
        playSound: _settings.enableSound,
        sound: _settings.enableSound
            ? RawResourceAndroidNotificationSound('notification_sound')
            : null,
        icon: _getNotificationIcon(type),
        color: _getNotificationColor(type),
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        styleInformation: BigTextStyleInformation(
          body,
          htmlFormatBigText: true,
          contentTitle: title,
          htmlFormatContentTitle: true,
          summaryText: 'إشعار من تطبيق الري',
          htmlFormatSummaryText: true,
        ),
        category: _getAndroidNotificationCategory(type),
        // إعدادات إضافية لإشعارات النظام
        fullScreenIntent: isImportant, // إشعار ملء الشاشة للمواعيد المهمة
        timeoutAfter: isImportant
            ? 30000
            : null, // انتهاء صلاحية الإشعار بعد 30 ثانية للمواعيد المهمة
        showProgress: false,
        indeterminate: false,
        autoCancel: true, // إغلاق تلقائي عند النقر
        ongoing: false, // لا يكون مستمراً
        silent: !_settings.enableSound, // صامت إذا كان الصوت معطل
        // إعدادات الإجراءات
        actions: actions?.map((action) => action.toAndroidAction()).toList(),
        // إعدادات الإشعارات المتقدمة
        channelShowBadge: true,
        onlyAlertOnce: false, // السماح بتكرار الإشعارات
        // إعدادات الألوان والتصميم
        colorized: true, // تمكين الألوان
        // إعدادات الإشعارات الكبيرة
      );

      // إعدادات iOS متقدمة
      final DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true, // عرض التنبيه
        presentBadge: true, // عرض الرقم على الأيقونة
        presentSound: _settings.enableSound, // تشغيل الصوت
        sound: _settings.enableSound ? 'notification_sound.wav' : null,
        categoryIdentifier: _getNotificationCategory(type),
        threadIdentifier: 'irrigation_app',
        // إعدادات إضافية لـ iOS
        interruptionLevel: isImportant
            ? InterruptionLevel.timeSensitive
            : InterruptionLevel.active,
      );

      final NotificationDetails details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // إنشاء معرف فريد للإشعار
      final notificationId =
          DateTime.now().millisecondsSinceEpoch.remainder(100000);

      // إرسال الإشعار
      await _notifications.show(
        notificationId,
        title,
        body,
        details,
        payload: payload,
      );

      // تشغيل التأثيرات الإضافية
      await _playNotificationEffects(type);

      // تسجيل الإشعار في السجل
      debugPrint('📝 إشعار مسجل: $title - $body - $type - ID: $notificationId');

      debugPrint('✅ تم إرسال إشعار النظام: $title (ID: $notificationId)');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال إشعار النظام: $e');
      rethrow;
    }
  }

  static Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    NotificationType type = NotificationType.info,
    bool isImportant = false,
    List<NotificationAction>? actions,
    bool repeat = false,
    RepeatInterval? repeatInterval,
  }) async {
    if (!_settings.enabled) return;
    try {
      final channelId = _getChannelId(type, isImportant);
      final notificationId =
          DateTime.now().millisecondsSinceEpoch.remainder(100000);
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        channelId,
        _getChannelName(type),
        channelDescription: _getChannelDescription(type),
        importance: isImportant ? Importance.max : Importance.high,
        priority: isImportant ? Priority.high : Priority.low,
        showWhen: true,
        when: scheduledDate.millisecondsSinceEpoch,
        enableLights: _settings.enableLED,
        enableVibration: _settings.enableVibration,
        playSound: _settings.enableSound,
        sound: _settings.enableSound
            ? RawResourceAndroidNotificationSound('notification_sound')
            : null,
        icon: _getNotificationIcon(type),
        color: _getNotificationColor(type),
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        styleInformation: BigTextStyleInformation(body),
        category: _getAndroidNotificationCategory(type),
      );
      final DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: _settings.enableSound,
        sound: _settings.enableSound ? 'notification_sound.wav' : null,
        categoryIdentifier: _getNotificationCategory(type),
        threadIdentifier: 'irrigation_app',
      );
      final NotificationDetails details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );
      if (repeat == true && repeatInterval != null) {
        await _notifications.periodicallyShow(
          notificationId,
          title,
          body,
          repeatInterval,
          details,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          payload: payload,
        );
      } else {
        await _notifications.zonedSchedule(
          notificationId,
          title,
          body,
          tz.TZDateTime.from(scheduledDate, tz.local),
          details,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          payload: payload,
        );
      }
      _scheduledNotifications[payload ?? title] = notificationId;
      debugPrint('✅ تم جدولة الإشعار: $title في ${scheduledDate.toString()}');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة الإشعار: $e');
    }
  }

  /// إلغاء إشعار مجدول
  static Future<void> cancelScheduledNotification(String identifier) async {
    try {
      final notificationId = _scheduledNotifications[identifier];
      if (notificationId != null) {
        await _notifications.cancel(notificationId);
        _scheduledNotifications.remove(identifier);
        debugPrint('✅ تم إلغاء الإشعار المجدول: $identifier');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الإشعار: $e');
    }
  }

  /// إلغاء جميع الإشعارات المجدولة
  static Future<void> cancelAllScheduledNotifications() async {
    try {
      await _notifications.cancelAll();
      _scheduledNotifications.clear();
      debugPrint('✅ تم إلغاء جميع الإشعارات المجدولة');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء جميع الإشعارات: $e');
    }
  }

  /// تشغيل تأثيرات الإشعار (صوت واهتزاز)
  static Future<void> _playNotificationEffects(NotificationType type) async {
    try {
      // تشغيل الاهتزاز
      if (_settings.enableVibration && await Vibration.hasVibrator() == true) {
        final pattern = _getVibrationPattern(type);
        await Vibration.vibrate(pattern: pattern);
      }

      // تشغيل الصوت
      if (_settings.enableSound) {
        await _playNotificationSound(type);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل تأثيرات الإشعار: $e');
    }
  }

  /// تشغيل صوت الإشعار
  static Future<void> _playNotificationSound(NotificationType type) async {
    try {
      final soundFile = _getNotificationSound(type);
      if (soundFile != null) {
        await _audioPlayer.setAsset(soundFile);
        await _audioPlayer.play();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تشغيل صوت الإشعار: $e');
    }
  }

  /// الحصول على نمط الاهتزاز
  static List<int> _getVibrationPattern(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return [0, 500, 200, 500, 200, 500]; // اهتزاز طويل
      case NotificationType.warning:
        return [0, 300, 200, 300]; // اهتزاز متوسط
      case NotificationType.reminder:
        return [0, 200, 100, 200]; // اهتزاز قصير
      case NotificationType.success:
        return [0, 100, 50, 100]; // اهتزاز خفيف
      case NotificationType.info:
        return [0, 150]; // اهتزاز بسيط
    }
  }

  /// الحصول على صوت الإشعار
  static String? _getNotificationSound(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return 'assets/sounds/error_notification.mp3';
      case NotificationType.warning:
        return 'assets/sounds/warning_notification.mp3';
      case NotificationType.reminder:
        return 'assets/sounds/reminder_notification.mp3';
      case NotificationType.success:
        return 'assets/sounds/success_notification.mp3';
      case NotificationType.info:
        return 'assets/sounds/info_notification.mp3';
    }
  }

  /// التحقق من ساعات الهدوء
  static bool _isInQuietHours() {
    if (!_settings.enableQuietHours) return false;

    final now = TimeOfDay.now();
    final start = _settings.quietHoursStart;
    final end = _settings.quietHoursEnd;

    if (start.hour < end.hour) {
      // نفس اليوم
      return now.hour >= start.hour && now.hour < end.hour;
    } else {
      // عبر منتصف الليل
      return now.hour >= start.hour || now.hour < end.hour;
    }
  }

  /// الحصول على معرف القناة
  static String _getChannelId(NotificationType type, bool isImportant) {
    if (isImportant) return 'system_channel';
    if (!_settings.enableSound) return 'silent_channel';
    switch (type) {
      case NotificationType.error:
        return 'system_channel';
      case NotificationType.warning:
        return 'important_channel';
      case NotificationType.reminder:
        return 'important_channel';
      case NotificationType.success:
        return 'normal_channel';
      case NotificationType.info:
        return 'normal_channel';
    }
  }

  /// الحصول على اسم القناة
  static String _getChannelName(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return 'تنبيهات الأخطاء';
      case NotificationType.warning:
        return 'تنبيهات التحذير';
      case NotificationType.reminder:
        return 'التذكيرات';
      case NotificationType.success:
        return 'تنبيهات النجاح';
      case NotificationType.info:
        return 'المعلومات';
    }
  }

  /// الحصول على وصف القناة
  static String _getChannelDescription(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return 'تنبيهات الأخطاء والمشاكل';
      case NotificationType.warning:
        return 'تنبيهات التحذير والتنبيهات';
      case NotificationType.reminder:
        return 'التذكيرات والمواعيد';
      case NotificationType.success:
        return 'تنبيهات النجاح والإنجازات';
      case NotificationType.info:
        return 'المعلومات والإشعارات العامة';
    }
  }

  /// الحصول على أيقونة الإشعار
  static String _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return 'ic_error';
      case NotificationType.warning:
        return 'ic_warning';
      case NotificationType.reminder:
        return 'ic_reminder';
      case NotificationType.success:
        return 'ic_success';
      case NotificationType.info:
        return 'ic_info';
    }
  }

  /// الحصول على لون الإشعار
  static Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return Colors.red;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.reminder:
        return Colors.blue;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.info:
        return Colors.grey;
    }
  }

  /// الحصول على فئة الإشعار
  static String _getNotificationCategory(NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return 'error';
      case NotificationType.warning:
        return 'warning';
      case NotificationType.reminder:
        return 'reminder';
      case NotificationType.success:
        return 'success';
      case NotificationType.info:
        return 'info';
    }
  }

  /// الحصول على فئة إشعار Android
  static AndroidNotificationCategory? _getAndroidNotificationCategory(
      NotificationType type) {
    switch (type) {
      case NotificationType.error:
        return AndroidNotificationCategory.error;
      case NotificationType.reminder:
        return AndroidNotificationCategory.reminder;
      case NotificationType.success:
        return AndroidNotificationCategory.status;
      case NotificationType.info:
        return AndroidNotificationCategory.message;
      default:
        return null;
    }
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('🔔 تم النقر على الإشعار: ${response.payload}');

    // يمكن إضافة منطق إضافي هنا
    // مثل فتح صفحة معينة أو تنفيذ إجراء
  }

  /// تحميل الإعدادات المحفوظة
  static Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _settings = NotificationSettings(
        enabled: prefs.getBool('notifications_enabled') ?? true,
        enableSound: prefs.getBool('notifications_sound') ?? true,
        enableVibration: prefs.getBool('notifications_vibration') ?? true,
        enableLED: prefs.getBool('notifications_led') ?? false,
        enableQuietHours: prefs.getBool('notifications_quiet_hours') ?? false,
        quietHoursStart: TimeOfDay(
          hour: prefs.getInt('quiet_hours_start_hour') ?? 22,
          minute: prefs.getInt('quiet_hours_start_minute') ?? 0,
        ),
        quietHoursEnd: TimeOfDay(
          hour: prefs.getInt('quiet_hours_end_hour') ?? 7,
          minute: prefs.getInt('quiet_hours_end_minute') ?? 0,
        ),
        enabledCategories:
            prefs.getStringList('notifications_enabled_categories') ??
                NotificationType.values.map((e) => e.toString()).toList(),
        soundType: prefs.getString('notifications_sound_type') ?? 'default',
      );

      debugPrint('✅ تم تحميل إعدادات الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الإشعارات: $e');
    }
  }

  /// حفظ الإعدادات
  static Future<void> saveSettings(NotificationSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool('notifications_enabled', settings.enabled);
      await prefs.setBool('notifications_sound', settings.enableSound);
      await prefs.setBool('notifications_vibration', settings.enableVibration);
      await prefs.setBool('notifications_led', settings.enableLED);
      await prefs.setBool(
          'notifications_quiet_hours', settings.enableQuietHours);
      await prefs.setInt(
          'quiet_hours_start_hour', settings.quietHoursStart.hour);
      await prefs.setInt(
          'quiet_hours_start_minute', settings.quietHoursStart.minute);
      await prefs.setInt('quiet_hours_end_hour', settings.quietHoursEnd.hour);
      await prefs.setInt(
          'quiet_hours_end_minute', settings.quietHoursEnd.minute);
      await prefs.setStringList(
          'notifications_enabled_categories', settings.enabledCategories);
      await prefs.setString('notifications_sound_type', settings.soundType);

      _settings = settings;

      debugPrint('✅ تم حفظ إعدادات الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات الإشعارات: $e');
    }
  }

  /// الحصول على الإعدادات الحالية
  static NotificationSettings get settings => _settings;

  /// اختبار الإشعارات
  static Future<void> testNotification() async {
    await showNotification(
      title: 'اختبار الإشعارات',
      body: 'هذا إشعار تجريبي لاختبار نظام الإشعارات',
      type: NotificationType.info,
      actions: [
        NotificationAction(
          id: 'test_action',
          title: 'إجراء تجريبي',
          icon: 'ic_test',
        ),
      ],
    );
  }
}

/// إعدادات الإشعارات
class NotificationSettings {
  final bool enabled;
  final bool enableSound;
  final bool enableVibration;
  final bool enableLED;
  final bool enableQuietHours;
  final TimeOfDay quietHoursStart;
  final TimeOfDay quietHoursEnd;
  final List<String> enabledCategories;
  final String soundType;

  const NotificationSettings({
    this.enabled = true,
    this.enableSound = true,
    this.enableVibration = true,
    this.enableLED = false,
    this.enableQuietHours = false,
    this.quietHoursStart = const TimeOfDay(hour: 22, minute: 0),
    this.quietHoursEnd = const TimeOfDay(hour: 7, minute: 0),
    this.enabledCategories = const [],
    this.soundType = 'default',
  });

  NotificationSettings copyWith({
    bool? enabled,
    bool? enableSound,
    bool? enableVibration,
    bool? enableLED,
    bool? enableQuietHours,
    TimeOfDay? quietHoursStart,
    TimeOfDay? quietHoursEnd,
    List<String>? enabledCategories,
    String? soundType,
  }) {
    return NotificationSettings(
      enabled: enabled ?? this.enabled,
      enableSound: enableSound ?? this.enableSound,
      enableVibration: enableVibration ?? this.enableVibration,
      enableLED: enableLED ?? this.enableLED,
      enableQuietHours: enableQuietHours ?? this.enableQuietHours,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      enabledCategories: enabledCategories ?? this.enabledCategories,
      soundType: soundType ?? this.soundType,
    );
  }
}

/// إجراء الإشعار
class NotificationAction {
  final String id;
  final String title;
  final String? icon;

  const NotificationAction({
    required this.id,
    required this.title,
    this.icon,
  });

  AndroidNotificationAction toAndroidAction() {
    return AndroidNotificationAction(
      id,
      title,
    );
  }
}

/// أنواع الإشعارات
enum NotificationType {
  info,
  warning,
  error,
  success,
  reminder,
}

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:untitled/presentation/routes/app_router.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/core/theme/theme_manager.dart';

class WateringApp extends StatelessWidget {
  const WateringApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ThemeManager(),
      child: Consumer<ThemeManager>(
        builder: (context, themeManager, child) {
          return MaterialApp(
            title: 'تطبيق إدارة التسقيات',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeManager.themeMode,
            // دعم اللغة العربية
            locale: const Locale('ar', 'SA'),
            supportedLocales: const [Locale('ar', 'SA')],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            // اتجاه التطبيق من اليمين إلى اليسار
            builder: (context, child) {
              return Directionality(
                  textDirection: TextDirection.rtl, child: child!);
            },
            // المسارات - تبدأ بصفحة تسجيل الدخول
            initialRoute: AppRouter.login,
            onGenerateRoute: AppRouter.onGenerateRoute,
          );
        },
      ),
    );
  }
}
